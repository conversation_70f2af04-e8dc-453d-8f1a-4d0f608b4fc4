from python.helpers.extension import Extension
from python.helpers.project_manager import Project<PERSON>anager
from agent import LoopData
from python.helpers.print_style import PrintStyle


class ActiveProjectContext(Extension):
    """Extension to inject active project context using Agent Zero prompt system."""

    async def execute(self, loop_data: LoopData = LoopData(), **kwargs):
        """Inject active project context variables and rendered template for template processing."""
        try:
            # Get active project for this agent
            active_project = self.agent.get_data("active_project")

            if not active_project:
                # Check if we need to notify about project deactivation
                if self.agent.get_data("project_context_refresh"):
                    loop_data.extras_temporary["project_switch_notification"] = "⚠️ **PROJECT CONTEXT CHANGED**: No active project. All projects have been deactivated."
                    self.agent.set_data("project_context_refresh", False)
                return

            # Load project data using ProjectManager
            project_manager = ProjectManager()
            project_entity = project_manager.get_project_by_id(active_project)

            if not project_entity:
                PrintStyle(font_color="orange", padding=True).print(
                    f"Warning: Active project '{active_project}' could not be loaded. Deactivating."
                )
                self.agent.set_data("active_project", None)
                self.agent.set_data("active_project_entity", None)
                return

            # Get file structure
            file_structure = project_manager.get_project_file_structure(active_project)

            # Render the active project context template with current data
            active_project_context = self.agent.read_prompt(
                "agent.context.active_project.md",
                project_name=project_entity.name,
                project_description=project_entity.description or "No description",
                project_instructions=getattr(project_entity, 'instructions', None),
                project_directory=project_entity.path,
                has_files=len(file_structure) > 0,
                file_structure=file_structure[:20],  # Limit to 20 items
                has_more_files=len(file_structure) > 20,
                additional_files_count=len(file_structure) - 20 if len(file_structure) > 20 else 0
            )

            # Add the rendered template to extras
            loop_data.extras_temporary["active_project_context"] = active_project_context

            # Check if we need to notify about project switch
            if self.agent.get_data("project_context_refresh"):
                loop_data.extras_temporary["project_switch_notification"] = f"🔄 **PROJECT CONTEXT CHANGED**: Now working in project '{project_entity.name}'. All subsequent work will be performed within this project context."
                self.agent.set_data("project_context_refresh", False)

            PrintStyle(font_color="cyan", padding=True).print(
                f"Active project context prepared: {project_entity.name}"
            )

        except Exception as e:
            PrintStyle(font_color="red", padding=True).print(
                f"Error preparing project context: {str(e)}"
            )

