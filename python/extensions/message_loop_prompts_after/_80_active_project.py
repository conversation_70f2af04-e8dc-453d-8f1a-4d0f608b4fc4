from python.helpers.extension import Extension
from agent import LoopData


class ActiveProjectContext(Extension):
    """
    DEPRECATED: This extension is no longer used.
    Project context is now handled by the system_prompt/_30_project_context.py extension.
    This file is kept to avoid breaking existing installations but does nothing.
    """

    async def execute(self, loop_data: LoopData = LoopData(), **kwargs):
        """No-op - project context is now handled in system prompt extensions."""
        pass

