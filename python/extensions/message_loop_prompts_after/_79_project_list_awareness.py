from python.helpers.extension import Extension
from python.helpers.project_manager import Project<PERSON>anager
from python.helpers.print_style import PrintStyle
from agent import LoopData


class ProjectListAwareness(Extension):
    """Extension to inject project list awareness using Agent Zero prompt system."""

    async def execute(self, loop_data: LoopData = LoopData(), **kwargs):
        """Inject project list awareness variables and rendered template for template processing."""
        try:
            # Load all available projects
            project_manager = ProjectManager()
            projects = project_manager.get_all_projects()

            # Prepare template variables
            project_list = []
            if projects:
                for project in projects:
                    project_data = {
                        "name": project.name,
                        "description": project.description or "No description"
                    }
                    # Add instructions preview if available
                    if hasattr(project, 'instructions') and project.instructions:
                        preview = project.instructions[:100]
                        if len(project.instructions) > 100:
                            preview += "..."
                        project_data["instructions_preview"] = preview
                    project_list.append(project_data)

            # Render the project list context template with current data
            project_list_context = self.agent.read_prompt(
                "agent.context.project_list.md",
                has_projects=len(projects) > 0,
                projects=project_list
            )

            # Add the rendered template to extras
            loop_data.extras_temporary["project_list_context"] = project_list_context

            PrintStyle(font_color="cyan", padding=True).print(
                f"Project list awareness prepared: {len(projects)} projects available"
            )

        except Exception as e:
            PrintStyle(font_color="red", padding=True).print(
                f"Error preparing project list awareness: {str(e)}"
            )

