"""
Unit tests for Agent Zero project validation logic.

Tests the ProjectManager class validation methods and ProjectEntity data model
to ensure proper validation of project names, descriptions, paths, and constraints.
"""

import pytest
import tempfile
import os
import json
import shutil
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from pathlib import Path

# Add project root to path for imports
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from python.helpers.project_manager import ProjectManager, ProjectEntity


class TestProjectEntity:
    """Test the ProjectEntity data class validation and behavior."""

    def test_project_entity_creation_with_required_fields(self):
        """Test creating a ProjectEntity with required fields."""
        project = ProjectEntity(
            id="test_123",
            name="Test Project",
            path="/path/to/project",
            description="A test project"
        )

        assert project.id == "test_123"
        assert project.name == "Test Project"
        assert project.path == "/path/to/project"
        assert project.description == "A test project"
        assert project.active is False  # Default value
        assert project.created_at is not None
        assert project.last_opened_at is not None

    def test_project_entity_auto_timestamps(self):
        """Test that timestamps are automatically set when not provided."""
        project = ProjectEntity(
            id="test_123",
            name="Test Project",
            path="/path/to/project",
            description="A test project"
        )

        # Check that timestamps are ISO format strings
        datetime.fromisoformat(project.created_at)
        datetime.fromisoformat(project.last_opened_at)

        # Initial timestamps should be equal
        assert project.created_at == project.last_opened_at

    def test_project_entity_custom_timestamps(self):
        """Test creating ProjectEntity with custom timestamps."""
        custom_time = "2024-01-01T12:00:00"
        project = ProjectEntity(
            id="test_123",
            name="Test Project",
            path="/path/to/project",
            description="A test project",
            created_at=custom_time,
            last_opened_at=custom_time
        )

        assert project.created_at == custom_time
        assert project.last_opened_at == custom_time

    def test_project_entity_to_dict(self):
        """Test converting ProjectEntity to dictionary."""
        project = ProjectEntity(
            id="test_123",
            name="Test Project",
            path="/path/to/project",
            description="A test project",
            active=True
        )

        project_dict = project.to_dict()

        assert isinstance(project_dict, dict)
        assert project_dict["id"] == "test_123"
        assert project_dict["name"] == "Test Project"
        assert project_dict["active"] is True
        assert "created_at" in project_dict
        assert "last_opened_at" in project_dict

    def test_project_entity_from_dict(self):
        """Test creating ProjectEntity from dictionary."""
        project_data = {
            "id": "test_123",
            "name": "Test Project",
            "path": "/path/to/project",
            "description": "A test project",
            "active": True,
            "created_at": "2024-01-01T12:00:00",
            "last_opened_at": "2024-01-01T13:00:00"
        }

        project = ProjectEntity.from_dict(project_data)

        assert project.id == "test_123"
        assert project.name == "Test Project"
        assert project.active is True
        assert project.created_at == "2024-01-01T12:00:00"
        assert project.last_opened_at == "2024-01-01T13:00:00"

    def test_project_entity_update_last_opened(self):
        """Test updating the last_opened_at timestamp."""
        project = ProjectEntity(
            id="test_123",
            name="Test Project",
            path="/path/to/project",
            description="A test project"
        )

        original_time = project.last_opened_at

        # Wait a bit to ensure timestamp difference
        import time
        time.sleep(0.001)

        project.update_last_opened()

        assert project.last_opened_at != original_time
        # Verify it's a valid ISO timestamp
        datetime.fromisoformat(project.last_opened_at)


class TestProjectManagerValidation:
    """Test ProjectManager validation logic."""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def project_manager(self, temp_dir):
        """Create a ProjectManager instance with temporary directory."""
        with patch('python.helpers.files.get_abs_path') as mock_get_abs_path:
            mock_get_abs_path.side_effect = lambda path: os.path.join(temp_dir, path) if path != "root" else temp_dir
            manager = ProjectManager()
            return manager

    def test_sanitize_project_name_valid(self, project_manager):
        """Test project name sanitization with valid names."""
        assert project_manager._sanitize_project_name("MyProject") == "MyProject"
        assert project_manager._sanitize_project_name("my-project_123") == "my-project_123"
        assert project_manager._sanitize_project_name("Project_With_Underscores") == "Project_With_Underscores"

    def test_sanitize_project_name_invalid_chars(self, project_manager):
        """Test project name sanitization with invalid characters."""
        assert project_manager._sanitize_project_name("my project!") == "my_project_"
        assert project_manager._sanitize_project_name("project@#$%") == "project____"
        assert project_manager._sanitize_project_name("my/project\\path") == "my_project_path"

    def test_sanitize_project_name_empty_or_invalid_start(self, project_manager):
        """Test project name sanitization with empty or invalid starting names."""
        assert project_manager._sanitize_project_name("") == "project_"
        assert project_manager._sanitize_project_name("-invalid") == "project_-invalid"
        assert project_manager._sanitize_project_name("_invalid") == "project__invalid"

    def test_sanitize_project_name_length_limit(self, project_manager):
        """Test project name sanitization length limit (50 chars)."""
        long_name = "a" * 60  # 60 characters
        sanitized = project_manager._sanitize_project_name(long_name)
        assert len(sanitized) == 50
        assert sanitized == "a" * 50

    def test_generate_project_id_format(self, project_manager):
        """Test project ID generation format."""
        project_id = project_manager._generate_project_id("Test Project")

        # Should contain sanitized name + timestamp
        assert "Test_Project_" in project_id
        assert len(project_id.split("_")) >= 3  # name parts + timestamp

        # Timestamp should be numeric
        timestamp_part = project_id.split("_")[-1]
        assert timestamp_part.isdigit()

    def test_create_project_validation_empty_name(self, project_manager):
        """Test project creation validation with empty name."""
        result = project_manager.create_project("", "Valid description")

        assert result["success"] is False
        assert "name is required" in result["error"].lower()

    def test_create_project_validation_whitespace_name(self, project_manager):
        """Test project creation validation with whitespace-only name."""
        result = project_manager.create_project("   ", "Valid description")

        assert result["success"] is False
        assert "name is required" in result["error"].lower()

    def test_create_project_validation_empty_description(self, project_manager):
        """Test project creation validation with empty description."""
        result = project_manager.create_project("Valid Name", "")

        assert result["success"] is False
        assert "description is required" in result["error"].lower()

    def test_create_project_validation_whitespace_description(self, project_manager):
        """Test project creation validation with whitespace-only description."""
        result = project_manager.create_project("Valid Name", "   ")

        assert result["success"] is False
        assert "description is required" in result["error"].lower()

    def test_create_project_duplicate_name_validation(self, project_manager):
        """Test project creation validation prevents duplicate names."""
        # Create first project
        result1 = project_manager.create_project("Test Project", "First project")
        assert result1["success"] is True

        # Attempt to create project with same name
        result2 = project_manager.create_project("Test Project", "Second project")
        assert result2["success"] is False
        assert "already exists" in result2["error"].lower()

    def test_create_project_duplicate_name_case_insensitive(self, project_manager):
        """Test project creation validation is case-insensitive for duplicates."""
        # Create first project
        result1 = project_manager.create_project("Test Project", "First project")
        assert result1["success"] is True

        # Attempt to create project with different case
        result2 = project_manager.create_project("test project", "Second project")
        assert result2["success"] is False
        assert "already exists" in result2["error"].lower()

    def test_create_project_success_strips_whitespace(self, project_manager):
        """Test project creation strips whitespace from name and description."""
        result = project_manager.create_project("  Test Project  ", "  Test description  ")

        assert result["success"] is True
        project_data = result["project"]
        assert project_data["name"] == "Test Project"
        assert project_data["description"] == "Test description"

    def test_update_project_validation_duplicate_name(self, project_manager):
        """Test project update validation prevents duplicate names."""
        # Create two projects
        result1 = project_manager.create_project("Project One", "First project")
        result2 = project_manager.create_project("Project Two", "Second project")

        assert result1["success"] is True
        assert result2["success"] is True

        project1_id = result1["project"]["id"]

        # Attempt to rename project1 to project2's name
        update_result = project_manager.update_project(project1_id, name="Project Two")

        assert update_result["success"] is False
        assert "already exists" in update_result["error"].lower()

    def test_update_project_validation_same_name_allowed(self, project_manager):
        """Test project update allows keeping the same name."""
        # Create project
        result = project_manager.create_project("Test Project", "Test description")
        assert result["success"] is True

        project_id = result["project"]["id"]

        # Update with same name but different description
        update_result = project_manager.update_project(
            project_id,
            name="Test Project",
            description="Updated description"
        )

        assert update_result["success"] is True
        assert update_result["project"]["description"] == "Updated description"

    def test_update_project_nonexistent_id(self, project_manager):
        """Test project update with non-existent project ID."""
        result = project_manager.update_project("nonexistent_id", name="New Name")

        assert result["success"] is False
        assert "not found" in result["error"].lower()

    def test_delete_project_nonexistent_id(self, project_manager):
        """Test project deletion with non-existent project ID."""
        result = project_manager.delete_project("nonexistent_id")

        assert result["success"] is False
        assert "not found" in result["error"].lower()

    @patch('python.helpers.project_manager.PrintStyle')
    def test_validation_error_logging(self, mock_print_style, project_manager):
        """Test that validation errors are properly logged."""
        # Trigger a validation error
        result = project_manager.create_project("", "Valid description")

        assert result["success"] is False
        # Verify error was returned in result
        assert "error" in result

    def test_project_name_length_edge_cases(self, project_manager):
        """Test project name validation edge cases around length limits."""
        # Test exactly 50 character name (should be allowed after sanitization)
        name_50_chars = "a" * 50
        result = project_manager.create_project(name_50_chars, "Valid description")
        assert result["success"] is True

        # Test over 50 character name (should be truncated)
        name_over_50 = "a" * 60
        result2 = project_manager.create_project(name_over_50, "Valid description 2")
        assert result2["success"] is True
        # Name should be truncated to 50 chars in the sanitized project ID
        project_id = result2["project"]["id"]
        sanitized_part = project_id.split("_")[0]  # First part before timestamp
        assert len(sanitized_part) <= 50

    def test_description_validation_edge_cases(self, project_manager):
        """Test project description validation edge cases."""
        # Test very long description (should be allowed)
        long_description = "x" * 1000
        result = project_manager.create_project("Test Project", long_description)
        assert result["success"] is True
        assert result["project"]["description"] == long_description

        # Test description with special characters (should be allowed)
        special_desc = "Description with @#$%^&*()[]{}|\\:;\"'<>?,./"
        result2 = project_manager.create_project("Test Project 2", special_desc)
        assert result2["success"] is True
        assert result2["project"]["description"] == special_desc

    def test_path_validation_custom_path(self, project_manager):
        """Test project creation with custom path."""
        custom_path = "/custom/project/path"
        result = project_manager.create_project(
            "Test Project",
            "Test description",
            path=custom_path
        )

        assert result["success"] is True
        assert result["project"]["path"] == custom_path

    def test_project_id_uniqueness(self, project_manager):
        """Test that generated project IDs are unique."""
        result1 = project_manager.create_project("Same Name", "Description 1")

        # Wait a moment to ensure different timestamp
        import time
        time.sleep(0.001)

        # Delete first project to allow same name
        project_manager.delete_project(result1["project"]["id"])

        result2 = project_manager.create_project("Same Name", "Description 2")

        assert result1["success"] is True
        assert result2["success"] is True
        assert result1["project"]["id"] != result2["project"]["id"]