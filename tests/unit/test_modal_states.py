"""
Unit tests for Agent Zero modal state management.

Tests the Alpine.js store-based modal state management including the full screen
input modal and generic modal state handling, animations, and data persistence.
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Add project root to path for imports
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))


class MockAlpineStore:
    """Mock Alpine.js store for testing modal state management."""

    def __init__(self, initial_state=None):
        self._data = initial_state or {}
        self._watchers = {}

    def get(self, key, default=None):
        return self._data.get(key, default)

    def set(self, key, value):
        self._data[key] = value
        # Trigger watchers
        if key in self._watchers:
            for watcher in self._watchers[key]:
                watcher(value)

    def watch(self, key, callback):
        if key not in self._watchers:
            self._watchers[key] = []
        self._watchers[key].append(callback)

    def __getitem__(self, key):
        return self._data[key]

    def __setitem__(self, key, value):
        self.set(key, value)

    def __contains__(self, key):
        return key in self._data

    def update(self, data):
        for key, value in data.items():
            self.set(key, value)


class TestFullScreenInputModal:
    """Test the full screen input modal state management."""

    @pytest.fixture
    def mock_dom_elements(self):
        """Create mock DOM elements for testing."""
        chat_input = Mock()
        chat_input.value = "Initial text"
        chat_input.focus = Mock()
        chat_input.dispatchEvent = Mock()

        full_screen_input = Mock()
        full_screen_input.focus = Mock()

        with patch('builtins.document') as mock_document:
            mock_document.getElementById.side_effect = lambda id: {
                'chat-input': chat_input,
                'full-screen-input': full_screen_input
            }.get(id)

            yield {
                'chat_input': chat_input,
                'full_screen_input': full_screen_input,
                'document': mock_document
            }

    @pytest.fixture
    def modal_state(self):
        """Create a full screen input modal state for testing."""
        return {
            'isOpen': False,
            'inputText': '',
            'wordWrap': True,
            'undoStack': [],
            'redoStack': [],
            'maxStackSize': 100,
            'lastSavedState': ''
        }

    def test_modal_initial_state(self, modal_state):
        """Test initial modal state values."""
        assert modal_state['isOpen'] is False
        assert modal_state['inputText'] == ''
        assert modal_state['wordWrap'] is True
        assert modal_state['undoStack'] == []
        assert modal_state['redoStack'] == []
        assert modal_state['maxStackSize'] == 100
        assert modal_state['lastSavedState'] == ''

    def test_open_modal_state_changes(self, modal_state, mock_dom_elements):
        """Test modal state changes when opening modal."""
        # Simulate opening modal
        modal_state['inputText'] = mock_dom_elements['chat_input'].value
        modal_state['lastSavedState'] = modal_state['inputText']
        modal_state['isOpen'] = True
        modal_state['undoStack'] = []
        modal_state['redoStack'] = []

        assert modal_state['isOpen'] is True
        assert modal_state['inputText'] == "Initial text"
        assert modal_state['lastSavedState'] == "Initial text"
        assert len(modal_state['undoStack']) == 0
        assert len(modal_state['redoStack']) == 0

    def test_close_modal_state_changes(self, modal_state, mock_dom_elements):
        """Test modal state changes when closing modal."""
        # Setup modal as open with modified text
        modal_state['isOpen'] = True
        modal_state['inputText'] = "Modified text"

        # Simulate closing modal
        mock_dom_elements['chat_input'].value = modal_state['inputText']
        modal_state['isOpen'] = False

        assert modal_state['isOpen'] is False
        assert mock_dom_elements['chat_input'].value == "Modified text"
        mock_dom_elements['chat_input'].dispatchEvent.assert_called()

    def test_undo_functionality(self, modal_state):
        """Test undo functionality in modal state."""
        # Setup state with history
        modal_state['inputText'] = "Current text"
        modal_state['lastSavedState'] = "Current text"
        modal_state['undoStack'] = ["Previous text", "Earlier text"]
        modal_state['redoStack'] = []

        # Simulate undo
        if modal_state['undoStack']:
            modal_state['redoStack'].append(modal_state['inputText'])
            modal_state['inputText'] = modal_state['undoStack'].pop()
            modal_state['lastSavedState'] = modal_state['inputText']

        assert modal_state['inputText'] == "Previous text"
        assert modal_state['lastSavedState'] == "Previous text"
        assert "Current text" in modal_state['redoStack']
        assert len(modal_state['undoStack']) == 1

    def test_redo_functionality(self, modal_state):
        """Test redo functionality in modal state."""
        # Setup state with redo history
        modal_state['inputText'] = "Current text"
        modal_state['lastSavedState'] = "Current text"
        modal_state['undoStack'] = ["Previous text"]
        modal_state['redoStack'] = ["Future text", "Later text"]

        # Simulate redo
        if modal_state['redoStack']:
            modal_state['undoStack'].append(modal_state['inputText'])
            modal_state['inputText'] = modal_state['redoStack'].pop()
            modal_state['lastSavedState'] = modal_state['inputText']

        assert modal_state['inputText'] == "Later text"
        assert modal_state['lastSavedState'] == "Later text"
        assert "Current text" in modal_state['undoStack']
        assert len(modal_state['redoStack']) == 1

    def test_update_history_functionality(self, modal_state):
        """Test history update functionality."""
        modal_state['inputText'] = "New text"
        modal_state['lastSavedState'] = "Old text"

        # Simulate update history
        if modal_state['lastSavedState'] != modal_state['inputText']:
            modal_state['undoStack'].append(modal_state['lastSavedState'])
            if len(modal_state['undoStack']) > modal_state['maxStackSize']:
                modal_state['undoStack'].pop(0)
            modal_state['redoStack'] = []
            modal_state['lastSavedState'] = modal_state['inputText']

        assert "Old text" in modal_state['undoStack']
        assert modal_state['lastSavedState'] == "New text"
        assert len(modal_state['redoStack']) == 0

    def test_history_size_limit(self, modal_state):
        """Test that undo stack respects size limit."""
        modal_state['maxStackSize'] = 3
        modal_state['undoStack'] = ["text1", "text2", "text3"]
        modal_state['lastSavedState'] = "text3"
        modal_state['inputText'] = "text4"

        # Simulate adding to history when at limit
        if modal_state['lastSavedState'] != modal_state['inputText']:
            modal_state['undoStack'].append(modal_state['lastSavedState'])
            if len(modal_state['undoStack']) > modal_state['maxStackSize']:
                modal_state['undoStack'].pop(0)  # Remove oldest
            modal_state['lastSavedState'] = modal_state['inputText']

        assert len(modal_state['undoStack']) == modal_state['maxStackSize']
        assert "text1" not in modal_state['undoStack']  # Oldest removed
        assert "text3" in modal_state['undoStack']  # Latest preserved

    def test_clear_text_functionality(self, modal_state):
        """Test clear text functionality."""
        modal_state['inputText'] = "Text to clear"
        modal_state['lastSavedState'] = "Previous state"

        # Simulate clear text (should update history first)
        if modal_state['inputText']:
            # Update history before clearing
            modal_state['undoStack'].append(modal_state['lastSavedState'])
            modal_state['inputText'] = ''
            modal_state['lastSavedState'] = ''

        assert modal_state['inputText'] == ''
        assert modal_state['lastSavedState'] == ''
        assert "Previous state" in modal_state['undoStack']

    def test_toggle_word_wrap(self, modal_state):
        """Test word wrap toggle functionality."""
        initial_wrap_state = modal_state['wordWrap']

        modal_state['wordWrap'] = not modal_state['wordWrap']

        assert modal_state['wordWrap'] != initial_wrap_state

        # Test toggle back
        modal_state['wordWrap'] = not modal_state['wordWrap']
        assert modal_state['wordWrap'] == initial_wrap_state

    def test_can_undo_can_redo_states(self, modal_state):
        """Test canUndo and canRedo computed properties logic."""
        # Initially should not be able to undo or redo
        can_undo = len(modal_state['undoStack']) > 0
        can_redo = len(modal_state['redoStack']) > 0

        assert can_undo is False
        assert can_redo is False

        # Add undo history
        modal_state['undoStack'] = ["previous text"]

        can_undo = len(modal_state['undoStack']) > 0
        assert can_undo is True

        # Add redo history
        modal_state['redoStack'] = ["future text"]

        can_redo = len(modal_state['redoStack']) > 0
        assert can_redo is True

    def test_modal_focus_behavior(self, mock_dom_elements):
        """Test modal focus behavior when opening."""
        with patch('builtins.setTimeout', side_effect=lambda func, delay: func()) as mock_timeout:
            # Simulate opening modal with focus
            mock_timeout(lambda: mock_dom_elements['full_screen_input'].focus(), 100)

            mock_dom_elements['full_screen_input'].focus.assert_called_once()


class TestGenericModal:
    """Test the generic modal state management."""

    @pytest.fixture
    def generic_modal_state(self):
        """Create a generic modal state for testing."""
        return {
            'isOpen': False,
            'isLoading': False,
            'title': '',
            'description': '',
            'html': ''
        }

    @pytest.fixture
    def mock_dom_elements_generic(self):
        """Create mock DOM elements for generic modal testing."""
        modal_el = Mock()
        modal_content = Mock()
        modal_content.className = ''
        modal_content.classList = Mock()

        mock_alpine_data = Mock()
        mock_alpine_data.isOpen = False
        mock_alpine_data.title = ''
        mock_alpine_data.description = ''
        mock_alpine_data.html = ''

        with patch('builtins.document') as mock_document:
            with patch('builtins.Alpine') as mock_alpine:
                mock_document.getElementById.side_effect = lambda id: {
                    'genericModal': modal_el,
                    'viewer': modal_content
                }.get(id)

                mock_alpine.$data.return_value = mock_alpine_data

                yield {
                    'modal_el': modal_el,
                    'modal_content': modal_content,
                    'document': mock_document,
                    'alpine': mock_alpine,
                    'alpine_data': mock_alpine_data
                }

    def test_generic_modal_initial_state(self, generic_modal_state):
        """Test initial generic modal state values."""
        assert generic_modal_state['isOpen'] is False
        assert generic_modal_state['isLoading'] is False
        assert generic_modal_state['title'] == ''
        assert generic_modal_state['description'] == ''
        assert generic_modal_state['html'] == ''

    def test_open_generic_modal(self, generic_modal_state, mock_dom_elements_generic):
        """Test opening generic modal with content."""
        title = "Test Modal"
        description = "This is a test modal"
        html_content = "<p>Test content</p>"
        content_classes = ["test-class", "another-class"]

        # Simulate opening modal
        alpine_data = mock_dom_elements_generic['alpine_data']
        modal_content = mock_dom_elements_generic['modal_content']

        alpine_data.isOpen = True
        alpine_data.title = title
        alpine_data.description = description
        alpine_data.html = html_content

        modal_content.className = 'modal-content'
        modal_content.classList.add(*content_classes)

        assert alpine_data.isOpen is True
        assert alpine_data.title == title
        assert alpine_data.description == description
        assert alpine_data.html == html_content
        modal_content.classList.add.assert_called_once_with(*content_classes)

    def test_close_generic_modal(self, generic_modal_state):
        """Test closing generic modal."""
        # Setup modal as open
        generic_modal_state['isOpen'] = True
        generic_modal_state['title'] = "Open Modal"

        # Simulate closing
        generic_modal_state['isOpen'] = False

        assert generic_modal_state['isOpen'] is False

    def test_generic_modal_loading_state(self, generic_modal_state):
        """Test generic modal loading state management."""
        assert generic_modal_state['isLoading'] is False

        # Simulate loading state
        generic_modal_state['isLoading'] = True
        assert generic_modal_state['isLoading'] is True

        # Simulate loading complete
        generic_modal_state['isLoading'] = False
        assert generic_modal_state['isLoading'] is False

    def test_generic_modal_content_updates(self, generic_modal_state):
        """Test updating generic modal content."""
        initial_state = generic_modal_state.copy()

        # Update content
        generic_modal_state['title'] = "Updated Title"
        generic_modal_state['description'] = "Updated Description"
        generic_modal_state['html'] = "<div>Updated HTML</div>"

        assert generic_modal_state['title'] != initial_state['title']
        assert generic_modal_state['description'] != initial_state['description']
        assert generic_modal_state['html'] != initial_state['html']

    def test_generic_modal_watcher_functionality(self):
        """Test Alpine.js watcher functionality for generic modal."""
        mock_store = MockAlpineStore({'isOpen': False})
        watcher_called = []

        def mock_watcher(value):
            watcher_called.append(value)

        mock_store.watch('isOpen', mock_watcher)

        # Trigger state change
        mock_store.set('isOpen', True)

        assert len(watcher_called) == 1
        assert watcher_called[0] is True

        # Trigger another change
        mock_store.set('isOpen', False)

        assert len(watcher_called) == 2
        assert watcher_called[1] is False


class TestModalStateTransitions:
    """Test modal state transitions and animations."""

    def test_modal_open_close_cycle(self):
        """Test complete modal open/close cycle."""
        modal_state = {
            'isOpen': False,
            'isAnimating': False,
            'animationClass': ''
        }

        # Open modal
        modal_state['isOpen'] = True
        modal_state['isAnimating'] = True
        modal_state['animationClass'] = 'modal-open'

        assert modal_state['isOpen'] is True
        assert modal_state['isAnimating'] is True

        # Animation complete
        modal_state['isAnimating'] = False
        modal_state['animationClass'] = ''

        assert modal_state['isAnimating'] is False
        assert modal_state['animationClass'] == ''

        # Close modal
        modal_state['isAnimating'] = True
        modal_state['animationClass'] = 'modal-close'

        # Close animation complete
        modal_state['isOpen'] = False
        modal_state['isAnimating'] = False
        modal_state['animationClass'] = ''

        assert modal_state['isOpen'] is False
        assert modal_state['isAnimating'] is False

    def test_multiple_modal_state_management(self):
        """Test managing multiple modal states simultaneously."""
        full_screen_modal = {'isOpen': False, 'inputText': ''}
        generic_modal = {'isOpen': False, 'title': ''}
        project_modal = {'isOpen': False, 'projectData': None}

        # Open full screen modal
        full_screen_modal['isOpen'] = True
        full_screen_modal['inputText'] = "Working on text"

        assert full_screen_modal['isOpen'] is True
        assert generic_modal['isOpen'] is False
        assert project_modal['isOpen'] is False

        # Open generic modal (should be able to coexist or close others depending on design)
        generic_modal['isOpen'] = True
        generic_modal['title'] = "Information Modal"

        assert generic_modal['isOpen'] is True

        # Close all modals
        full_screen_modal['isOpen'] = False
        generic_modal['isOpen'] = False
        project_modal['isOpen'] = False

        assert not any([
            full_screen_modal['isOpen'],
            generic_modal['isOpen'],
            project_modal['isOpen']
        ])

    def test_modal_data_persistence(self):
        """Test modal data persistence during state changes."""
        modal_state = {
            'isOpen': False,
            'formData': {
                'name': 'Test Project',
                'description': 'Test Description',
                'path': '/test/path'
            },
            'isDirty': False
        }

        # Open modal
        modal_state['isOpen'] = True

        # Modify form data
        modal_state['formData']['name'] = 'Updated Project'
        modal_state['isDirty'] = True

        # Close modal without saving (data should persist)
        modal_state['isOpen'] = False

        assert modal_state['formData']['name'] == 'Updated Project'
        assert modal_state['isDirty'] is True

        # Reopen modal (data should still be there)
        modal_state['isOpen'] = True

        assert modal_state['formData']['name'] == 'Updated Project'

    def test_modal_validation_state(self):
        """Test modal form validation state management."""
        form_state = {
            'isValid': True,
            'errors': {},
            'fields': {
                'name': 'Valid Name',
                'email': '<EMAIL>'
            }
        }

        # Simulate validation error
        form_state['fields']['email'] = 'invalid-email'
        form_state['isValid'] = False
        form_state['errors']['email'] = 'Invalid email format'

        assert form_state['isValid'] is False
        assert 'email' in form_state['errors']

        # Fix validation error
        form_state['fields']['email'] = '<EMAIL>'
        form_state['isValid'] = True
        form_state['errors'] = {}

        assert form_state['isValid'] is True
        assert len(form_state['errors']) == 0

    def test_modal_async_operations(self):
        """Test modal state during async operations."""
        modal_state = {
            'isOpen': True,
            'isLoading': False,
            'error': None,
            'data': None
        }

        # Start async operation
        modal_state['isLoading'] = True
        modal_state['error'] = None

        assert modal_state['isLoading'] is True
        assert modal_state['error'] is None

        # Simulate successful completion
        modal_state['isLoading'] = False
        modal_state['data'] = {'result': 'success'}

        assert modal_state['isLoading'] is False
        assert modal_state['data'] is not None

        # Simulate error scenario
        modal_state['isLoading'] = True
        modal_state['data'] = None

        # Error occurred
        modal_state['isLoading'] = False
        modal_state['error'] = 'Operation failed'

        assert modal_state['isLoading'] is False
        assert modal_state['error'] is not None
        assert modal_state['data'] is None


class TestModalIntegration:
    """Integration tests for modal state management."""

    def test_modal_store_integration(self):
        """Test integration between different modal stores."""
        # Mock Alpine.js store registration
        stores = {}

        def mock_alpine_store(name, state):
            stores[name] = MockAlpineStore(state)

        # Register stores
        mock_alpine_store('fullScreenInputModal', {
            'isOpen': False,
            'inputText': ''
        })

        mock_alpine_store('genericModal', {
            'isOpen': False,
            'title': ''
        })

        # Test store interactions
        assert 'fullScreenInputModal' in stores
        assert 'genericModal' in stores

        # Open full screen modal
        stores['fullScreenInputModal']['isOpen'] = True
        stores['fullScreenInputModal']['inputText'] = 'Test content'

        assert stores['fullScreenInputModal']['isOpen'] is True
        assert stores['genericModal']['isOpen'] is False

    def test_modal_event_coordination(self):
        """Test coordination between modal events."""
        event_log = []

        def log_event(event_type, modal_name):
            event_log.append(f"{event_type}:{modal_name}")

        # Simulate modal events
        log_event("open", "fullScreenModal")
        log_event("close", "fullScreenModal")
        log_event("open", "genericModal")
        log_event("close", "genericModal")

        expected_events = [
            "open:fullScreenModal",
            "close:fullScreenModal",
            "open:genericModal",
            "close:genericModal"
        ]

        assert event_log == expected_events

    def test_modal_memory_management(self):
        """Test modal memory management and cleanup."""
        modal_instances = []

        class MockModal:
            def __init__(self, name):
                self.name = name
                self.watchers = []
                self.timers = []

            def add_watcher(self, callback):
                self.watchers.append(callback)

            def add_timer(self, timer_id):
                self.timers.append(timer_id)

            def cleanup(self):
                self.watchers.clear()
                self.timers.clear()

        # Create modal instances
        modal1 = MockModal("fullScreen")
        modal2 = MockModal("generic")

        modal_instances.extend([modal1, modal2])

        # Add watchers and timers
        modal1.add_watcher(lambda: None)
        modal1.add_timer("timer1")

        modal2.add_watcher(lambda: None)
        modal2.add_timer("timer2")

        # Cleanup all modals
        for modal in modal_instances:
            modal.cleanup()

        assert len(modal1.watchers) == 0
        assert len(modal1.timers) == 0
        assert len(modal2.watchers) == 0
        assert len(modal2.timers) == 0