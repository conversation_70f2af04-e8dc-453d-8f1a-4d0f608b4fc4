"""
Component Integration Test Runner
Comprehensive test runner for component system integration and memory leak verification
"""

import pytest
import sys
import time
from pathlib import Path
from tests.test_config import TEST_CONFIG


def run_component_integration_tests():
    """Run all component integration tests and generate comprehensive report"""

    print("=" * 80)
    print("AGENT ZERO COMPONENT SYSTEM INTEGRATION TESTS")
    print("=" * 80)
    print()

    # Test modules to run
    test_modules = [
        "tests/ui/test_component_integration.py",
        "tests/ui/test_alpine_store_lifecycle.py",
        "tests/ui/test_memory_leak_prevention.py",
        "tests/ui/test_store_isolation.py"
    ]

    print("Test Modules to Execute:")
    for module in test_modules:
        print(f"  - {module}")
    print()

    print("Test Configuration:")
    for key, value in TEST_CONFIG.items():
        print(f"  {key}: {value}")
    print()

    # Check if required dependencies are available
    try:
        from selenium import webdriver
        print("✓ Selenium WebDriver available")
    except ImportError:
        print("✗ Selenium WebDriver not available - some tests will be skipped")

    try:
        from selenium.webdriver.chrome.options import Options
        print("✓ Chrome WebDriver options available")
    except ImportError:
        print("✗ Chrome WebDriver options not available")

    print()
    print("-" * 80)
    print("STARTING TEST EXECUTION")
    print("-" * 80)

    # Run tests with pytest
    test_args = [
        "-v",  # Verbose output
        "--tb=short",  # Short traceback format
        "--durations=10",  # Show 10 slowest tests
        "--strict-markers",  # Strict marker checking
        "-x",  # Stop on first failure
    ]

    # Add test modules
    test_args.extend(test_modules)

    # Run the tests
    start_time = time.time()
    exit_code = pytest.main(test_args)
    end_time = time.time()

    print()
    print("-" * 80)
    print("TEST EXECUTION SUMMARY")
    print("-" * 80)
    print(f"Total execution time: {end_time - start_time:.2f} seconds")
    print(f"Exit code: {exit_code}")

    if exit_code == 0:
        print("✓ ALL TESTS PASSED")
        print()
        print("Component system integration verification SUCCESSFUL:")
        print("  ✓ Component loading and cleanup working correctly")
        print("  ✓ Alpine.js store integration functioning properly")
        print("  ✓ Memory leak prevention mechanisms active")
        print("  ✓ Store isolation maintained")
        print("  ✓ No global pollution detected")
        print("  ✓ Event listener cleanup working")
        print("  ✓ Component disposal mechanisms functional")
    else:
        print("✗ SOME TESTS FAILED")
        print()
        print("Issues detected in component system integration:")
        print("  Check test output above for specific failure details")

    print()
    print("=" * 80)

    return exit_code


if __name__ == "__main__":
    exit_code = run_component_integration_tests()
    sys.exit(exit_code)