"""
Integration test for hierarchical modal structure
"""
import pytest
import asyncio
import time
from tests.test_config import TEST_CONFIG


class TestModalHierarchy:
    """Test hierarchical project modal structure"""

    def setup_method(self):
        """Setup test environment before each test method"""
        # Import browser automation tools
        try:
            from playwright.sync_api import sync_playwright
            self.playwright_context = sync_playwright()
            self.playwright = self.playwright_context.start()
            self.browser = None
            self.page = None
        except ImportError:
            pytest.skip("Playwright not available for UI testing")

    def teardown_method(self):
        """Cleanup after each test method"""
        try:
            if hasattr(self, 'page') and self.page:
                self.page.close()
        except:
            pass

        try:
            if hasattr(self, 'browser') and self.browser:
                self.browser.close()
        except:
            pass

        try:
            if hasattr(self, 'playwright_context') and self.playwright_context:
                self.playwright_context.__exit__(None, None, None)
        except:
            pass

    def launch_browser_and_navigate(self):
        """Launch browser and navigate to the application"""
        try:
            self.browser = self.playwright.chromium.launch(headless=True)
            self.page = self.browser.new_page()

            # Try to navigate to the app first
            base_url = TEST_CONFIG.get("app_url", "http://localhost:8000")
            try:
                self.page.goto(base_url, timeout=5000)
                # Wait for page to load
                self.page.wait_for_load_state("networkidle", timeout=5000)
                return self.page
            except Exception:
                # If server is not running, use demo file
                import os
                demo_file = os.path.join(os.path.dirname(__file__), "modal_test_demo.html")
                if os.path.exists(demo_file):
                    self.page.goto(f"file://{demo_file}")
                    self.page.wait_for_load_state("networkidle", timeout=5000)
                    return self.page
                else:
                    pytest.skip("Server not running and demo file not found")

        except Exception as e:
            if "net::ERR_CONNECTION_REFUSED" in str(e) or "Connection refused" in str(e) or "TimeoutError" in str(e):
                pytest.skip("Server not running - cannot test modal functionality")
            else:
                raise

    def run_ui_test(self, test_func):
        """Wrapper for running UI tests with proper error handling"""
        try:
            return test_func()
        except Exception as e:
            if "net::ERR_CONNECTION_REFUSED" in str(e) or "Connection refused" in str(e) or "TimeoutError" in str(e):
                pytest.skip("Server not running - cannot test modal functionality")
            else:
                raise

    def test_project_list_modal_opens(self):
        """Test that project management modal opens correctly"""
        try:
            page = self.launch_browser_and_navigate()

            # Look for project indicator in the page
            project_indicator = page.locator(".project-indicator").first

            if not project_indicator.is_visible():
                pytest.skip("Project indicator not found - modal system may not be implemented yet")

            # Click the project indicator to open dropdown
            project_indicator.click()

            # Wait for dropdown to appear
            dropdown = page.locator(".project-indicator-dropdown")
            page.wait_for_selector(".project-indicator-dropdown.visible", timeout=2000)

            # Look for manage projects button
            manage_button = page.locator("text=Manage Projects").first
            if not manage_button.is_visible():
                pytest.skip("Manage Projects button not found - may not be implemented yet")

            # Click manage projects to open modal
            manage_button.click()

            # Wait for project list modal to appear
            modal_overlay = page.locator(".project-modal-overlay.visible")
            modal = page.locator(".project-list-modal .project-modal")

            # Verify modal is visible
            page.wait_for_selector(".project-modal-overlay.visible", timeout=3000)
            assert modal_overlay.is_visible(), "Project list modal overlay should be visible"
            assert modal.is_visible(), "Project list modal should be visible"

            # Verify modal has expected structure
            modal_header = modal.locator(".project-modal-header")
            modal_content = modal.locator(".project-modal-content")

            assert modal_header.is_visible(), "Modal should have header"
            assert modal_content.is_visible(), "Modal should have content area"

            # Verify modal title
            modal_title = modal_header.locator(".project-modal-title")
            assert modal_title.inner_text().strip(), "Modal should have a title"

        except Exception as e:
            if "net::ERR_CONNECTION_REFUSED" in str(e) or "Connection refused" in str(e):
                pytest.skip("Server not running - cannot test modal functionality")
            else:
                raise

    def test_project_detail_modal_hierarchy(self):
        """Test that detail modal opens over list modal"""
        page = self.launch_browser_and_navigate()

        # First open the project list modal
        project_indicator = page.locator(".project-indicator").first

        if not project_indicator.is_visible():
            pytest.skip("Project indicator not found - modal system may not be implemented yet")

        # Open project list modal
        project_indicator.click()
        page.wait_for_selector(".project-indicator-dropdown.visible", timeout=2000)

        manage_button = page.locator("text=Manage Projects").first
        if not manage_button.is_visible():
            pytest.skip("Manage Projects button not found")

        manage_button.click()
        page.wait_for_selector(".project-modal-overlay.visible", timeout=3000)

        # Wait for project cards to load
        page.wait_for_selector(".project-list-card", timeout=2000)

        # Look for project cards to click
        project_cards = page.locator(".project-list-card")
        card_count = project_cards.count()

        if card_count == 0:
            pytest.skip("No project cards found - may need test data")

        # Click on the first project card
        first_card = project_cards.first
        first_card.click()

        # Wait for detail modal to appear
        detail_modal_overlay = page.locator(".project-detail-modal .project-modal-overlay")
        page.wait_for_selector(".project-detail-modal .project-modal-overlay.visible", timeout=3000)

        # Verify both modals are visible (hierarchical structure)
        list_modal = page.locator(".project-list-modal .project-modal")
        detail_modal = page.locator(".project-detail-modal .project-modal")

        assert list_modal.is_visible(), "Project list modal should remain visible behind detail modal"
        assert detail_modal.is_visible(), "Project detail modal should be visible on top"

        # Verify z-index layering (detail modal should be on top)
        list_modal_z = page.evaluate("() => window.getComputedStyle(document.querySelector('.project-list-modal .project-modal-overlay')).zIndex")
        detail_modal_z = page.evaluate("() => window.getComputedStyle(document.querySelector('.project-detail-modal .project-modal-overlay')).zIndex")

        # Convert to integers for comparison (handle 'auto' case)
        list_z = int(list_modal_z) if list_modal_z.isdigit() else 0
        detail_z = int(detail_modal_z) if detail_modal_z.isdigit() else 0

        assert detail_z > list_z, f"Detail modal z-index ({detail_z}) should be higher than list modal z-index ({list_z})"

    def test_modal_navigation_flow(self):
        """Test complete modal navigation flow"""
        page = self.launch_browser_and_navigate()

        # 1. Open project list modal
        project_indicator = page.locator(".project-indicator").first
        if not project_indicator.is_visible():
            pytest.skip("Project indicator not found")

        project_indicator.click()
        page.wait_for_selector(".project-indicator-dropdown.visible", timeout=2000)

        manage_button = page.locator("text=Manage Projects").first
        if not manage_button.is_visible():
            pytest.skip("Manage Projects button not found")

        manage_button.click()
        page.wait_for_selector(".project-modal-overlay.visible", timeout=3000)

        # Verify list modal is open
        list_modal = page.locator(".project-list-modal .project-modal")
        assert list_modal.is_visible(), "List modal should be open"

        # 2. Open project detail modal
        page.wait_for_selector(".project-list-card", timeout=2000)
        project_cards = page.locator(".project-list-card")

        if project_cards.count() == 0:
            pytest.skip("No project cards found")

        project_cards.first.click()
        page.wait_for_selector(".project-detail-modal .project-modal-overlay.visible", timeout=3000)

        # Verify detail modal is open
        detail_modal = page.locator(".project-detail-modal .project-modal")
        assert detail_modal.is_visible(), "Detail modal should be open"
        assert list_modal.is_visible(), "List modal should still be visible behind detail modal"

        # 3. Close detail modal - should return to list modal
        detail_close_btn = page.locator(".project-detail-modal .project-modal-close")
        detail_close_btn.click()

        # Wait for detail modal to close
        page.wait_for_function("() => !document.querySelector('.project-detail-modal .project-modal-overlay.visible')", timeout=2000)

        # Verify detail modal is closed and list modal is still visible
        assert not page.locator(".project-detail-modal .project-modal-overlay.visible").is_visible(), "Detail modal should be closed"
        assert list_modal.is_visible(), "List modal should still be visible"

        # 4. Close list modal - should return to main interface
        list_close_btn = page.locator(".project-list-modal .project-modal-close")
        list_close_btn.click()

        # Wait for list modal to close
        page.wait_for_function("() => !document.querySelector('.project-list-modal .project-modal-overlay.visible')", timeout=2000)

        # Verify all modals are closed
        assert not page.locator(".project-modal-overlay.visible").is_visible(), "All modals should be closed"

        # Verify we're back to main interface (project indicator should be visible)
        assert project_indicator.is_visible(), "Should return to main interface with project indicator visible"

    def test_modal_escape_key_handling(self):
        """Test ESC key handling in modal hierarchy"""
        page = self.launch_browser_and_navigate()

        # 1. Open project list modal
        project_indicator = page.locator(".project-indicator").first
        if not project_indicator.is_visible():
            pytest.skip("Project indicator not found")

        project_indicator.click()
        page.wait_for_selector(".project-indicator-dropdown.visible", timeout=2000)

        manage_button = page.locator("text=Manage Projects").first
        if not manage_button.is_visible():
            pytest.skip("Manage Projects button not found")

        manage_button.click()
        page.wait_for_selector(".project-modal-overlay.visible", timeout=3000)

        # 2. Open project detail modal
        page.wait_for_selector(".project-list-card", timeout=2000)
        project_cards = page.locator(".project-list-card")

        if project_cards.count() == 0:
            pytest.skip("No project cards found")

        project_cards.first.click()
        page.wait_for_selector(".project-detail-modal .project-modal-overlay.visible", timeout=3000)

        # Verify both modals are open
        list_modal = page.locator(".project-list-modal .project-modal")
        detail_modal = page.locator(".project-detail-modal .project-modal")
        assert detail_modal.is_visible(), "Detail modal should be open"
        assert list_modal.is_visible(), "List modal should be open behind detail modal"

        # 3. Press ESC - should close detail modal, return to list
        page.keyboard.press("Escape")

        # Wait for detail modal to close
        page.wait_for_function("() => !document.querySelector('.project-detail-modal .project-modal-overlay.visible')", timeout=2000)

        # Verify detail modal is closed but list modal remains open
        assert not page.locator(".project-detail-modal .project-modal-overlay.visible").is_visible(), "Detail modal should be closed after ESC"
        assert list_modal.is_visible(), "List modal should still be visible after closing detail modal"

        # 4. Press ESC again - should close list modal, return to main
        page.keyboard.press("Escape")

        # Wait for list modal to close
        page.wait_for_function("() => !document.querySelector('.project-list-modal .project-modal-overlay.visible')", timeout=2000)

        # Verify all modals are closed
        assert not page.locator(".project-modal-overlay.visible").is_visible(), "All modals should be closed after second ESC"

        # Verify we're back to main interface
        assert project_indicator.is_visible(), "Should return to main interface with project indicator visible"

    def test_modal_backdrop_click_handling(self):
        """Test backdrop click behavior in hierarchical modals"""
        page = self.launch_browser_and_navigate()

        # 1. Open hierarchical modals (list → detail)
        project_indicator = page.locator(".project-indicator").first
        if not project_indicator.is_visible():
            pytest.skip("Project indicator not found")

        # Open project list modal
        project_indicator.click()
        page.wait_for_selector(".project-indicator-dropdown.visible", timeout=2000)

        manage_button = page.locator("text=Manage Projects").first
        if not manage_button.is_visible():
            pytest.skip("Manage Projects button not found")

        manage_button.click()
        page.wait_for_selector(".project-modal-overlay.visible", timeout=3000)

        # Open project detail modal
        page.wait_for_selector(".project-list-card", timeout=2000)
        project_cards = page.locator(".project-list-card")

        if project_cards.count() == 0:
            pytest.skip("No project cards found")

        project_cards.first.click()
        page.wait_for_selector(".project-detail-modal .project-modal-overlay.visible", timeout=3000)

        # Verify both modals are open
        list_modal = page.locator(".project-list-modal .project-modal")
        detail_modal = page.locator(".project-detail-modal .project-modal")
        assert detail_modal.is_visible(), "Detail modal should be open"
        assert list_modal.is_visible(), "List modal should be open behind detail modal"

        # 2. Click backdrop of detail modal - should close detail, show list
        detail_overlay = page.locator(".project-detail-modal .project-modal-overlay")

        # Click on the overlay but not on the modal content (backdrop click)
        # Get the overlay bounding box and click in a corner away from the modal
        overlay_box = detail_overlay.bounding_box()
        modal_box = detail_modal.bounding_box()

        # Click in the top-left corner of the overlay (outside the modal content)
        backdrop_x = overlay_box['x'] + 10
        backdrop_y = overlay_box['y'] + 10

        # Ensure we're clicking outside the modal content
        if (backdrop_x < modal_box['x'] or backdrop_x > modal_box['x'] + modal_box['width'] or
            backdrop_y < modal_box['y'] or backdrop_y > modal_box['y'] + modal_box['height']):
            page.mouse.click(backdrop_x, backdrop_y)
        else:
            # If we can't find a good backdrop area, click the overlay directly
            # and use JavaScript to simulate backdrop click
            page.evaluate("document.querySelector('.project-detail-modal .project-modal-overlay').click()")

        # Wait for detail modal to close
        page.wait_for_function("() => !document.querySelector('.project-detail-modal .project-modal-overlay.visible')", timeout=2000)

        # Verify detail modal is closed but list modal remains open
        assert not page.locator(".project-detail-modal .project-modal-overlay.visible").is_visible(), "Detail modal should be closed after backdrop click"
        assert list_modal.is_visible(), "List modal should still be visible after closing detail modal"

        # 3. Click backdrop of list modal - should close list, return to main
        list_overlay = page.locator(".project-list-modal .project-modal-overlay")

        # Similar backdrop click for list modal
        overlay_box = list_overlay.bounding_box()
        list_modal_box = list_modal.bounding_box()

        backdrop_x = overlay_box['x'] + 10
        backdrop_y = overlay_box['y'] + 10

        if (backdrop_x < list_modal_box['x'] or backdrop_x > list_modal_box['x'] + list_modal_box['width'] or
            backdrop_y < list_modal_box['y'] or backdrop_y > list_modal_box['y'] + list_modal_box['height']):
            page.mouse.click(backdrop_x, backdrop_y)
        else:
            page.evaluate("document.querySelector('.project-list-modal .project-modal-overlay').click()")

        # Wait for list modal to close
        page.wait_for_function("() => !document.querySelector('.project-list-modal .project-modal-overlay.visible')", timeout=2000)

        # Verify all modals are closed
        assert not page.locator(".project-modal-overlay.visible").is_visible(), "All modals should be closed after backdrop click"

        # Verify we're back to main interface
        assert project_indicator.is_visible(), "Should return to main interface with project indicator visible"

    def test_modal_animations_and_transitions(self):
        """Test modal animations and transitions work correctly"""
        page = self.launch_browser_and_navigate()

        # Test modal opening animation
        project_indicator = page.locator(".project-indicator").first
        if not project_indicator.is_visible():
            pytest.skip("Project indicator not found")

        # Measure time for modal to appear
        start_time = time.time()
        project_indicator.click()
        page.wait_for_selector(".project-indicator-dropdown.visible", timeout=2000)

        manage_button = page.locator("text=Manage Projects").first
        if not manage_button.is_visible():
            pytest.skip("Manage Projects button not found")

        manage_button.click()
        page.wait_for_selector(".project-modal-overlay.visible", timeout=3000)
        open_time = time.time() - start_time

        # Animation should complete within reasonable time (under 1 second)
        assert open_time < 1.0, f"Modal opening took too long: {open_time}s"

        # Test that modal has proper CSS classes for animation
        modal = page.locator(".project-list-modal .project-modal")
        modal_classes = page.evaluate("() => document.querySelector('.project-list-modal .project-modal').className")

        # Should have CSS classes that enable transitions
        assert "project-modal" in modal_classes, "Modal should have proper CSS classes"

        # Test modal closing animation
        start_time = time.time()
        close_btn = page.locator(".project-list-modal .project-modal-close")
        close_btn.click()

        page.wait_for_function("() => !document.querySelector('.project-list-modal .project-modal-overlay.visible')", timeout=2000)
        close_time = time.time() - start_time

        # Closing animation should also complete quickly
        assert close_time < 1.0, f"Modal closing took too long: {close_time}s"

        # Verify we're back to main interface
        assert project_indicator.is_visible(), "Should return to main interface after modal closes"