"""
Component System Verification Tests
Core tests that verify component system structure and integration without browser automation
"""

import pytest
import json
from pathlib import Path
from tests.test_config import TEST_CONFIG


class TestComponentSystemStructure:
    """Test component system file structure and code integrity"""

    def test_component_files_exist(self):
        """Test that all required component files exist"""
        project_root = Path(__file__).parent.parent.parent

        required_files = [
            "webui/js/components.js",
            "webui/js/AlpineStore.js",
            "webui/js/project-store.js",
            "webui/components/projects/project-store.js"
        ]

        for file_path in required_files:
            full_path = project_root / file_path
            assert full_path.exists(), f"Required component file missing: {file_path}"

    def test_component_system_code_structure(self):
        """Test component system code structure and exports"""
        project_root = Path(__file__).parent.parent.parent

        # Test components.js structure
        components_file = project_root / "webui/js/components.js"
        components_content = components_file.read_text()

        assert "importComponent" in components_content, "components.js should export importComponent"
        assert "loadComponents" in components_content, "components.js should export loadComponents"
        assert "getParentAttributes" in components_content, "components.js should export getParentAttributes"
        assert "componentCache" in components_content, "components.js should have componentCache"
        assert "importLocks" in components_content, "components.js should have importLocks"

    def test_alpine_store_code_structure(self):
        """Test Alpine store helper code structure"""
        project_root = Path(__file__).parent.parent.parent

        # Test AlpineStore.js structure
        alpine_store_file = project_root / "webui/js/AlpineStore.js"
        alpine_store_content = alpine_store_file.read_text()

        assert "createStore" in alpine_store_content, "AlpineStore.js should export createStore"
        assert "getStore" in alpine_store_content, "AlpineStore.js should export getStore"
        assert "new Proxy" in alpine_store_content, "AlpineStore.js should use Proxy for reactivity"
        assert "Alpine.store" in alpine_store_content, "AlpineStore.js should integrate with Alpine"

    def test_project_store_code_structure(self):
        """Test project store code structure and methods"""
        project_root = Path(__file__).parent.parent.parent

        # Test project-store.js structure
        project_store_file = project_root / "webui/js/project-store.js"
        project_store_content = project_store_file.read_text()

        # Required API methods
        api_methods = [
            "loadProjects", "getProject", "createProject",
            "updateProject", "deleteProject", "activateProject"
        ]
        for method in api_methods:
            assert method in project_store_content, f"project-store.js should have {method} method"

        # Required utility methods
        utility_methods = [
            "getProjectById", "getAllProjects", "getActiveProject",
            "isProjectActive", "getProjectsCount", "validateProjectData"
        ]
        for method in utility_methods:
            assert method in project_store_content, f"project-store.js should have {method} method"

        # Required event system
        event_methods = ["addEventListener", "removeEventListener", "emit"]
        for method in event_methods:
            assert method in project_store_content, f"project-store.js should have {method} method"

        # Required state properties
        state_properties = [
            "projects", "activeProject", "loading", "error",
            "creating", "updating", "deleting", "activating"
        ]
        for prop in state_properties:
            assert prop in project_store_content, f"project-store.js should have {prop} property"

    def test_component_store_integration(self):
        """Test that component store properly integrates with main store"""
        project_root = Path(__file__).parent.parent.parent

        # Test component store integration
        component_store_file = project_root / "webui/components/projects/project-store.js"
        component_store_content = component_store_file.read_text()

        assert "import" in component_store_content, "Component store should use ES6 imports"
        assert "/js/project-store.js" in component_store_content, "Should import main project store"
        assert "export" in component_store_content, "Component store should re-export store"
        assert "globalThis.projectStore" in component_store_content, "Should add to global scope for compatibility"


class TestComponentSystemIntegrity:
    """Test component system code integrity and patterns"""

    def test_memory_management_patterns(self):
        """Test that memory management patterns are present in code"""
        project_root = Path(__file__).parent.parent.parent

        # Check components.js for memory management
        components_file = project_root / "webui/js/components.js"
        components_content = components_file.read_text()

        # Should have cache management
        assert "componentCache" in components_content, "Should have component cache"
        assert "importLocks" in components_content, "Should have import locks for race condition prevention"

        # Should have cleanup patterns
        assert "removeChild" in components_content or "remove()" in components_content, "Should have DOM cleanup"

        # Check project store for memory management
        project_store_file = project_root / "webui/js/project-store.js"
        project_store_content = project_store_file.read_text()

        # Should have event listener management
        assert "listeners" in project_store_content, "Should have event listeners management"
        assert "removeEventListener" in project_store_content, "Should have listener removal"
        assert "Map" in project_store_content, "Should use Map for efficient listener storage"

    def test_error_handling_patterns(self):
        """Test that error handling patterns are present"""
        project_root = Path(__file__).parent.parent.parent

        files_to_check = [
            "webui/js/components.js",
            "webui/js/project-store.js",
            "webui/js/AlpineStore.js"
        ]

        for file_path in files_to_check:
            full_path = project_root / file_path
            content = full_path.read_text()

            assert "try" in content and "catch" in content, f"{file_path} should have error handling"
            assert "console.error" in content or "throw" in content, f"{file_path} should handle errors appropriately"

    def test_component_isolation_patterns(self):
        """Test that component isolation patterns are implemented"""
        project_root = Path(__file__).parent.parent.parent

        # Check components.js for isolation
        components_file = project_root / "webui/js/components.js"
        components_content = components_file.read_text()

        # Should use proper module patterns
        assert "export" in components_content, "Should use ES6 module exports"

        # Should avoid global pollution
        global_assignments = components_content.count("window.") + components_content.count("globalThis.")
        assert global_assignments <= 3, f"Should minimize global assignments, found {global_assignments}"

        # Check AlpineStore.js for isolation
        alpine_store_file = project_root / "webui/js/AlpineStore.js"
        alpine_store_content = alpine_store_file.read_text()

        # Should track stores properly
        assert "stores.set" in alpine_store_content, "Should track store instances"
        assert "stores.get" in alpine_store_content, "Should allow store retrieval"

    def test_reactivity_patterns(self):
        """Test that Alpine.js reactivity patterns are properly implemented"""
        project_root = Path(__file__).parent.parent.parent

        # Check AlpineStore.js for reactivity
        alpine_store_file = project_root / "webui/js/AlpineStore.js"
        alpine_store_content = alpine_store_file.read_text()

        # Should use Proxy for reactivity
        assert "new Proxy" in alpine_store_content, "Should use Proxy for reactive updates"
        assert "set(" in alpine_store_content and "get(" in alpine_store_content, "Should have proper proxy handlers"

        # Should integrate with Alpine
        assert "Alpine.store" in alpine_store_content, "Should integrate with Alpine store system"
        assert "alpine:init" in alpine_store_content, "Should handle Alpine initialization"


class TestComponentSystemConfiguration:
    """Test component system configuration and setup"""

    def test_project_management_api_structure(self):
        """Test that project management API structure is properly defined"""
        project_root = Path(__file__).parent.parent.parent

        # Check if API file exists
        api_file = project_root / "python/api/projects.py"
        if api_file.exists():
            # If API file exists, verify basic structure
            api_content = api_file.read_text()

            assert "def " in api_content, "API file should have function definitions"
            assert "route" in api_content or "app." in api_content, "Should have route definitions"

    def test_project_management_helpers(self):
        """Test that project management helpers are properly structured"""
        project_root = Path(__file__).parent.parent.parent

        # Check if helper file exists
        helper_file = project_root / "python/helpers/project_manager.py"
        if helper_file.exists():
            helper_content = helper_file.read_text()

            assert "class " in helper_content or "def " in helper_content, "Helper file should have definitions"

    def test_integration_extensions(self):
        """Test that integration extensions are properly structured"""
        project_root = Path(__file__).parent.parent.parent

        extension_patterns = [
            "python/extensions/*project*.py",
            "python/extensions/*79*.py",
            "python/extensions/*80*.py",
            "python/extensions/*81*.py"
        ]

        for pattern in extension_patterns:
            extension_files = list(project_root.glob(pattern))
            if extension_files:
                for ext_file in extension_files:
                    content = ext_file.read_text()
                    assert "def " in content or "class " in content, f"Extension {ext_file.name} should have definitions"


class TestComponentSystemDocumentation:
    """Test component system documentation and specifications"""

    def test_specification_files_exist(self):
        """Test that specification files exist and are properly structured"""
        project_root = Path(__file__).parent.parent.parent

        spec_files = [
            ".specify/spec.md",
            "CLAUDE.md"
        ]

        for spec_file in spec_files:
            full_path = project_root / spec_file
            if full_path.exists():
                content = full_path.read_text()
                assert len(content) > 100, f"Specification file {spec_file} should have substantial content"

    def test_component_css_exists(self):
        """Test that component CSS exists"""
        project_root = Path(__file__).parent.parent.parent

        css_file = project_root / "webui/css/project-components.css"
        if css_file.exists():
            css_content = css_file.read_text()

            # Should have project-related styles
            assert ".project" in css_content, "CSS should have project-related styles"


class TestComponentSystemValidation:
    """Validation tests for overall component system health"""

    def test_no_obvious_memory_leaks_in_code(self):
        """Test code for obvious memory leak patterns"""
        project_root = Path(__file__).parent.parent.parent

        js_files = [
            "webui/js/components.js",
            "webui/js/AlpineStore.js",
            "webui/js/project-store.js"
        ]

        for js_file in js_files:
            full_path = project_root / js_file
            content = full_path.read_text()

            # Check for proper cleanup patterns
            if "addEventListener" in content:
                assert "removeEventListener" in content, f"{js_file} should have event listener cleanup"

            # Check for proper variable scoping
            global_var_count = content.count("var ") + content.count("window.") - content.count("// window.")
            assert global_var_count < 10, f"{js_file} should minimize global variables"

    def test_component_system_exports_structure(self):
        """Test that component system has proper export structure"""
        project_root = Path(__file__).parent.parent.parent

        # Check main component file exports
        components_file = project_root / "webui/js/components.js"
        components_content = components_file.read_text()

        required_exports = ["importComponent", "loadComponents", "getParentAttributes"]
        for export in required_exports:
            assert f"export" in components_content and export in components_content, f"Should export {export}"

        # Check store file exports
        store_file = project_root / "webui/js/AlpineStore.js"
        store_content = store_file.read_text()

        store_exports = ["createStore", "getStore"]
        for export in store_exports:
            assert f"export" in store_content and export in store_content, f"Should export {export}"

    def test_integration_completeness(self):
        """Test that integration appears complete based on file structure"""
        project_root = Path(__file__).parent.parent.parent

        # Core files should exist
        core_files = [
            "webui/js/components.js",
            "webui/js/AlpineStore.js",
            "webui/js/project-store.js",
            "webui/components/projects/project-store.js"
        ]

        missing_files = []
        for file_path in core_files:
            if not (project_root / file_path).exists():
                missing_files.append(file_path)

        assert len(missing_files) == 0, f"Missing core files: {missing_files}"

        # Test files should exist
        test_files = [
            "tests/ui/test_component_integration.py",
            "tests/ui/test_alpine_store_lifecycle.py",
            "tests/ui/test_memory_leak_prevention.py",
            "tests/ui/test_store_isolation.py"
        ]

        existing_test_files = []
        for file_path in test_files:
            if (project_root / file_path).exists():
                existing_test_files.append(file_path)

        assert len(existing_test_files) >= 3, f"Should have comprehensive test coverage, found: {existing_test_files}"


def test_overall_component_system_health():
    """Overall component system health check"""
    project_root = Path(__file__).parent.parent.parent

    health_report = {
        "core_files_present": True,
        "test_files_present": True,
        "code_quality_checks": True,
        "integration_complete": True
    }

    # Quick file existence check
    critical_files = [
        "webui/js/components.js",
        "webui/js/AlpineStore.js",
        "webui/js/project-store.js"
    ]

    for file_path in critical_files:
        if not (project_root / file_path).exists():
            health_report["core_files_present"] = False

    # Check test coverage
    test_files = list((project_root / "tests/ui").glob("test_*.py"))
    if len(test_files) < 3:
        health_report["test_files_present"] = False

    # Overall health assessment
    overall_health = all(health_report.values())

    print("\n" + "="*60)
    print("COMPONENT SYSTEM HEALTH REPORT")
    print("="*60)
    for key, value in health_report.items():
        status = "✓ PASS" if value else "✗ FAIL"
        print(f"{key.replace('_', ' ').title()}: {status}")
    print("="*60)
    print(f"Overall System Health: {'✓ HEALTHY' if overall_health else '✗ ISSUES DETECTED'}")
    print("="*60)

    assert overall_health, "Component system health check failed"