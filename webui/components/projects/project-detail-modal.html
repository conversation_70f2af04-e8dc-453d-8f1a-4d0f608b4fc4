<html>

<head>
    <title>Project Details</title>

    <!-- Import the alpine store -->
    <script type="module">
        import { store } from "/components/projects/project-store.js";
    </script>
</head>

<body>
    <!-- This construct of x-data + x-if is used to ensure the component is only rendered when the store is available -->
    <div x-data>
        <template x-if="$store.projectStore && $store.projectStore.showDetailModal">
            <div @keydown="$store.projectStore.handleDetailEscapeKey($event)" class="project-detail-overlay" @click.self="$store.projectStore.closeDetailModal()">
                <!-- Detail modal container -->
                <div class="project-detail-container" @click.stop>
                    <!-- Header with close button -->
                    <div class="detail-header">
                        <h3>
                            <span x-show="$store.projectStore.detailMode === 'create'">Create New Project</span>
                            <span x-show="$store.projectStore.detailMode === 'edit'">Edit Project</span>
                            <span x-show="$store.projectStore.detailMode === 'view'">Project Details</span>
                        </h3>
                        <button class="btn btn-icon" @click="$store.projectStore.closeDetailModal()" title="Close">
                            <span class="icon material-symbols-outlined">close</span>
                        </button>
                    </div>

                    <!-- Loading spinner -->
                    <div class="loading-spinner" x-show="$store.projectStore.isDetailLoading">
                        <span class="icon material-symbols-outlined spin">progress_activity</span>
                        <span x-text="$store.projectStore.detailLoadingText"></span>
                    </div>

                    <!-- Error message -->
                    <div class="error-message" x-show="$store.projectStore.detailError" x-text="$store.projectStore.detailError"></div>

                    <!-- Main content when not loading -->
                    <div class="detail-content" x-show="!$store.projectStore.isDetailLoading">

                        <!-- View mode -->
                        <div x-show="$store.projectStore.detailMode === 'view'">
                            <div class="project-detail-info">
                                <div class="detail-field">
                                    <label>Project Name</label>
                                    <div class="field-value" x-text="$store.projectStore.selectedProject?.name"></div>
                                </div>

                                <div class="detail-field">
                                    <label>Description</label>
                                    <div class="field-value" x-text="$store.projectStore.selectedProject?.description"></div>
                                </div>


                                <div class="detail-field" x-show="$store.projectStore.selectedProject?.instructions">
                                    <label>Instructions</label>
                                    <div class="field-value multiline" x-text="$store.projectStore.selectedProject?.instructions"></div>
                                </div>

                                <div class="detail-field">
                                    <label>Status</label>
                                    <div class="field-value">
                                        <span class="status-badge"
                                              :class="$store.projectStore.isProjectActive($store.projectStore.selectedProject?.name) ? 'active' : 'inactive'">
                                            <span x-show="$store.projectStore.isProjectActive($store.projectStore.selectedProject?.name)">Active</span>
                                            <span x-show="!$store.projectStore.isProjectActive($store.projectStore.selectedProject?.name)">Inactive</span>
                                        </span>
                                    </div>
                                </div>

                                <div class="detail-field" x-show="$store.projectStore.selectedProject?.created_at">
                                    <label>Created</label>
                                    <div class="field-value" x-text="$store.projectStore.formatDate($store.projectStore.selectedProject?.created_at)"></div>
                                </div>

                                <div class="detail-field" x-show="$store.projectStore.selectedProject?.updated_at">
                                    <label>Last Updated</label>
                                    <div class="field-value" x-text="$store.projectStore.formatDate($store.projectStore.selectedProject?.updated_at)"></div>
                                </div>
                            </div>

                            <!-- View mode actions -->
                            <div class="detail-actions">
                                <template x-if="!$store.projectStore.isProjectActive($store.projectStore.selectedProject?.name)">
                                    <button class="btn btn-primary" @click="$store.projectStore.activateProjectFromDetail()">
                                        <span class="icon material-symbols-outlined">play_arrow</span>
                                        Activate Project
                                    </button>
                                </template>
                                <template x-if="$store.projectStore.isProjectActive($store.projectStore.selectedProject?.name)">
                                    <button class="btn btn-secondary" @click="$store.projectStore.deactivateProjectFromDetail()">
                                        <span class="icon material-symbols-outlined">stop</span>
                                        Deactivate Project
                                    </button>
                                </template>
                                <button class="btn btn-secondary" @click="$store.projectStore.switchToEditMode()">
                                    <span class="icon material-symbols-outlined">edit</span>
                                    Edit Project
                                </button>
                            </div>
                        </div>

                        <!-- Create/Edit mode form -->
                        <div x-show="$store.projectStore.detailMode === 'create' || $store.projectStore.detailMode === 'edit'">
                            <div class="project-detail-form">
                                <div class="form-group">
                                    <label for="detail-project-name">Project Name *</label>
                                    <input type="text"
                                           id="detail-project-name"
                                           x-model="$store.projectStore.detailForm.name"
                                           placeholder="Enter project name"
                                           class="form-control"
                                           :readonly="$store.projectStore.detailMode === 'edit'">
                                    <div class="field-help">A unique name to identify this project</div>
                                </div>

                                <div class="form-group">
                                    <label for="detail-project-description">Description *</label>
                                    <textarea id="detail-project-description"
                                              x-model="$store.projectStore.detailForm.description"
                                              placeholder="Brief description of the project"
                                              class="form-control"
                                              rows="3"></textarea>
                                    <div class="field-help">A brief summary of what this project is about</div>
                                </div>


                                <div class="form-group">
                                    <label for="detail-project-instructions">Instructions</label>
                                    <textarea id="detail-project-instructions"
                                              x-model="$store.projectStore.detailForm.instructions"
                                              placeholder="Detailed instructions for agents working on this project"
                                              class="form-control"
                                              rows="8"></textarea>
                                    <div class="field-help">Detailed guidance for AI agents when working on this project</div>
                                </div>

                                <!-- Form validation messages -->
                                <div class="validation-messages" x-show="$store.projectStore.validationErrors.length > 0">
                                    <template x-for="error in $store.projectStore.validationErrors" :key="error">
                                        <div class="validation-error" x-text="error"></div>
                                    </template>
                                </div>
                            </div>

                            <!-- Form actions -->
                            <div class="detail-actions">
                                <button class="btn btn-secondary" @click="$store.projectStore.cancelDetailForm()">
                                    Cancel
                                </button>
                                <button class="btn btn-primary" @click="$store.projectStore.submitDetailForm()">
                                    <span class="icon material-symbols-outlined" x-show="$store.projectStore.detailMode === 'create'">add</span>
                                    <span class="icon material-symbols-outlined" x-show="$store.projectStore.detailMode === 'edit'">save</span>
                                    <span x-show="$store.projectStore.detailMode === 'create'">Create Project</span>
                                    <span x-show="$store.projectStore.detailMode === 'edit'">Save Changes</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <!-- Component styles -->
    <style>
        /* Modal overlay - positioned above the main project modal */
        .project-detail-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 2000; /* Higher than main modal */
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            backdrop-filter: blur(2px);
        }

        /* Modal overlay now handles click-to-close with @click.self */

        /* Detail modal container */
        .project-detail-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            max-height: 90vh;
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1rem 1rem 1.5rem;
            border-bottom: 1px solid var(--color-border);
            background: var(--color-background);
        }

        .detail-header h3 {
            margin: 0;
            color: var(--color-text);
            font-size: 1.125rem;
            font-weight: 500;
        }

        /* Content area */
        .detail-content {
            flex: 1;
            padding: 1rem 1.5rem;
            overflow-y: auto;
        }

        .detail-content::-webkit-scrollbar {
            width: 6px;
        }

        .detail-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .detail-content::-webkit-scrollbar-thumb {
            background-color: rgba(155, 155, 155, 0.3);
            border-radius: 6px;
        }

        .detail-content::-webkit-scrollbar-thumb:hover {
            background-color: rgba(155, 155, 155, 0.5);
        }

        /* Loading spinner */
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 3rem 1.5rem;
            font-style: italic;
            color: var(--text-color-secondary);
        }

        .loading-spinner .icon {
            font-size: 1.5rem;
            margin-right: 10px;
            color: var(--accent-color);
        }

        /* Error message */
        .error-message {
            background-color: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            color: #dc3545;
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
            text-align: center;
        }

        /* Project detail info (view mode) */
        .project-detail-info {
            display: flex;
            flex-direction: column;
            gap: 1.25rem;
        }

        .detail-field {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .detail-field label {
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .field-value {
            color: var(--text-color-secondary);
            line-height: 1.5;
            padding: 0.75rem;
            background: var(--bg-color-secondary);
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }

        .field-value.multiline {
            white-space: pre-wrap;
            min-height: 100px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Status badge */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.active {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.4);
        }

        .status-badge.inactive {
            background: rgba(108, 117, 125, 0.2);
            color: #6c757d;
            border: 1px solid rgba(108, 117, 125, 0.4);
        }

        /* Form styles */
        .project-detail-form {
            display: flex;
            flex-direction: column;
            gap: 1.25rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-group label {
            font-weight: 500;
            color: var(--color-text);
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }

        .form-control {
            padding: 0.75rem 1rem;
            border: 1px solid var(--color-border);
            border-radius: 4px;
            background-color: var(--color-background);
            color: var(--color-text);
            font-family: inherit;
            font-size: 0.875rem;
            resize: vertical;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--color-primary);
            background-color: var(--color-background);
        }

        .form-control:readonly {
            background-color: var(--color-panel);
            color: var(--color-secondary);
            cursor: not-allowed;
        }

        .field-help {
            font-size: 0.8rem;
            color: var(--color-secondary);
            font-style: italic;
            margin-top: 0.25rem;
        }

        /* Validation styles */
        .validation-messages {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 6px;
            padding: 0.75rem;
        }

        .validation-error {
            color: #dc3545;
            font-size: 0.85rem;
            margin-bottom: 0.25rem;
        }

        .validation-error:last-child {
            margin-bottom: 0;
        }

        /* Actions area */
        .detail-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--color-border);
        }

        /* Button styles */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            border: none;
            border-radius: 6px;
            font-family: inherit;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
            justify-content: center;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-primary {
            background-color: #2196F3;
            color: white;
            border: none;
        }

        .btn-primary:hover:not(:disabled) {
            background-color: #1976D2;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: transparent;
            color: #cf6679;
            border: none;
        }

        .btn-secondary:hover:not(:disabled) {
            background-color: #cf6679;
            color: white;
            border-color: #cf6679;
        }

        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            border-radius: 4px;
            background: transparent;
            border: none;
            color: var(--color-text);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0.7;
        }

        .btn-icon:hover {
            background: transparent;
            opacity: 1;
        }

        .btn .icon {
            font-size: 1.1rem;
        }

        .btn-icon .icon {
            font-size: 1.25rem;
        }

        /* Responsive design */
        @media (max-width: 640px) {
            .project-detail-overlay {
                padding: 0.5rem;
            }

            .project-detail-container {
                max-width: 100%;
                max-height: 95vh;
            }

            .detail-header {
                padding: 1rem;
            }

            .detail-content {
                padding: 1rem;
            }

            .detail-actions {
                flex-direction: column-reverse;
            }

            .btn {
                min-width: auto;
                width: 100%;
            }
        }

        /* Animation for spinner */
        .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Modal enter/exit animations */
        .project-detail-overlay {
            animation: modal-fade-in 0.2s ease-out;
        }

        .project-detail-container {
            animation: modal-slide-in 0.2s ease-out;
        }

        @keyframes modal-fade-in {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes modal-slide-in {
            from {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
    </style>

</body>

</html>