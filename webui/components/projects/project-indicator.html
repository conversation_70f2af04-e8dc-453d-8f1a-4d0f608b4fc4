<!DOCTYPE html>
<html>

<head>
    <title>Project Indicator Component</title>

    <script type="module">
        import { store } from "/components/projects/project-store.js";
    </script>
</head>

<body>
    <div x-data="projectIndicator()"
         x-init="init()"
         @click.away="closeDropdown()"
         class="project-indicator-container">

        <!-- Main project indicator button -->
        <div class="project-indicator-button"
             @click="toggleDropdown()"
             :class="{ 'active': dropdownOpen, 'has-project': !!$store.projectStore?.activeProject }">

            <!-- Project icon -->
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="project-icon">
                <path d="M10 4H4c-1.11 0-2 .89-2 2v3c0 1.11.89 2 2 2h6c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zM10 13H4c-1.11 0-2 .89-2 2v3c0 1.11.89 2 2 2h6c1.11 0 2-.89 2-2v-3c0-1.11-.89-2-2-2zM20 4h-6c-1.11 0-2 .89-2 2v3c0 1.11.89 2 2 2h6c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zM20 13h-6c-1.11 0-2 .89-2 2v3c0 1.11.89 2 2 2h6c1.11 0 2-.89 2-2v-3c0-1.11-.89-2-2-2z"/>
            </svg>

            <!-- Project name or "No Project" -->
            <span class="project-name"
                  x-text="$store.projectStore?.activeProject?.name || 'No Project'">
            </span>

            <!-- Status indicator -->
            <div class="project-status-dot"
                 :class="{ 'active': !!$store.projectStore?.activeProject }">
            </div>

            <!-- Dropdown arrow -->
            <svg class="dropdown-arrow"
                 :class="{ 'rotated': dropdownOpen }"
                 viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
        </div>

        <!-- Dropdown content -->
        <div class="project-dropdown"
             x-show="dropdownOpen"
             x-transition:enter="transition ease-out duration-100"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-1 scale-100"
             x-transition:leave="transition ease-in duration-75"
             x-transition:leave-start="opacity-1 scale-100"
             x-transition:leave-end="opacity-0 scale-95">

            <!-- Create New Project at top -->
            <div class="dropdown-section create-project-section">
                <button class="create-project-btn" @click="createNewProject()">
                    <svg viewBox="0 0 20 20" fill="currentColor" class="create-project-icon">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    Create New Project
                </button>
            </div>

            <!-- Active project section -->
            <div class="dropdown-section" x-show="$store.projectStore?.activeProject">
                <div class="section-header">Active Project</div>
                <div class="active-project-card">
                    <div class="project-card-header">
                        <span class="project-card-name"
                              x-text="$store.projectStore?.activeProject?.name">
                        </span>
                        <div class="project-card-actions">
                            <button class="action-btn"
                                    @click="editProject($store.projectStore.activeProject)"
                                    title="Edit project">
                                <svg viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                </svg>
                            </button>
                            <button class="action-btn danger"
                                    @click="deactivateProject()"
                                    title="Deactivate project">
                                <svg viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="project-card-description"
                         x-show="$store.projectStore?.activeProject?.description"
                         x-text="$store.projectStore?.activeProject?.description">
                    </div>
                </div>
            </div>

            <!-- Divider -->
            <div class="dropdown-divider" x-show="$store.projectStore?.activeProject && projects.length > 0"></div>

            <!-- Available projects section -->
            <div class="dropdown-section" x-show="projects.length > 0">
                <div class="section-header">
                    <span>Available Projects</span>
                    <span class="project-count" x-text="`(${projects.length})`"></span>
                </div>
                <div class="projects-list">
                    <template x-for="project in projects" :key="project.id">
                        <div class="project-list-item"
                             :class="{ 'is-active': project.active }"
                             @click="activateProject(project)">
                            <div class="project-item-main">
                                <div class="project-item-info">
                                    <span class="project-item-name" x-text="project.name"></span>
                                </div>
                                <div class="project-item-actions" @click.stop>
                                    <button class="action-btn small"
                                            @click="editProject(project)"
                                            title="Edit project">
                                        <svg viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                        </svg>
                                    </button>
                                    <button class="action-btn small danger"
                                            @click="deleteProject(project)"
                                            title="Delete project">
                                        <svg viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9zM4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h.01a1 1 0 100-2H10zm3 0a1 1 0 100 2h.01a1 1 0 100-2H13z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="project-item-description"
                                 x-show="project.description"
                                 x-text="project.description">
                            </div>
                        </div>
                    </template>
                </div>
            </div>

            <!-- No projects message -->
            <div class="dropdown-section" x-show="projects.length === 0 && !$store.projectStore?.loading">
                <div class="no-projects-message">
                    <svg viewBox="0 0 24 24" fill="currentColor" class="no-projects-icon">
                        <path d="M10 4H4c-1.11 0-2 .89-2 2v3c0 1.11.89 2 2 2h6c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zM10 13H4c-1.11 0-2 .89-2 2v3c0 1.11.89 2 2 2h6c1.11 0 2-.89 2-2v-3c0-1.11-.89-2-2-2zM20 4h-6c-1.11 0-2 .89-2 2v3c0 1.11.89 2 2 2h6c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zM20 13h-6c-1.11 0-2 .89-2 2v3c0 1.11.89 2 2 2h6c1.11 0 2-.89 2-2v-3c0-1.11-.89-2-2-2z"/>
                    </svg>
                    <span>No projects available</span>
                    <small>Create your first project to get started</small>
                </div>
            </div>

            <!-- Loading state -->
            <div class="dropdown-section" x-show="$store.projectStore?.loading">
                <div class="loading-message">
                    <div class="loading-spinner"></div>
                    <span>Loading projects...</span>
                </div>
            </div>

        </div>
    </div>

    <script>
        function projectIndicator() {
            return {
                dropdownOpen: false,
                projects: [],

                init() {
                    // Initialize component when the project store is ready
                    this.$nextTick(() => {
                        if (this.$store.projectStore) {
                            this.loadProjects();
                            this.setupEventListeners();
                        }
                    });
                },

                setupEventListeners() {
                    if (!this.$store.projectStore) return;

                    // Listen for project store events
                    this.$store.projectStore.addEventListener('projectsLoaded', (data) => {
                        this.projects = data.projects || [];
                    });

                    this.$store.projectStore.addEventListener('projectCreated', (project) => {
                        this.loadProjects();
                    });

                    this.$store.projectStore.addEventListener('projectUpdated', (project) => {
                        this.loadProjects();
                    });

                    this.$store.projectStore.addEventListener('projectDeleted', (project) => {
                        this.loadProjects();
                    });

                    this.$store.projectStore.addEventListener('projectActivated', (project) => {
                        this.loadProjects();
                        this.closeDropdown();
                    });

                    this.$store.projectStore.addEventListener('projectDeactivated', (previousActive) => {
                        this.loadProjects();
                        this.closeDropdown();
                    });
                },

                loadProjects() {
                    if (this.$store.projectStore) {
                        this.projects = this.$store.projectStore.getAllProjects();
                    }
                },

                async toggleDropdown() {
                    this.dropdownOpen = !this.dropdownOpen;
                    if (this.dropdownOpen && this.$store.projectStore) {
                        try {
                            await this.$store.projectStore.loadProjects();
                            this.loadProjects();
                        } catch (error) {
                            console.error('Failed to load projects:', error);
                        }
                    }
                },

                closeDropdown() {
                    this.dropdownOpen = false;
                },

                async activateProject(project) {
                    if (project.active) {
                        this.closeDropdown();
                        return;
                    }

                    try {
                        await this.$store.projectStore.activateProject(project.id);
                    } catch (error) {
                        console.error('Failed to activate project:', error);
                        // TODO: Show user notification
                    }
                },

                async deactivateProject() {
                    try {
                        await this.$store.projectStore.deactivateProject();
                        this.closeDropdown();
                    } catch (error) {
                        console.error('Failed to deactivate project:', error);
                        // TODO: Show user notification
                    }
                },

                editProject(project) {
                    if (this.$store.projectStore && project) {
                        this.$store.projectStore.openDetailModal('edit', project);
                    }
                    this.closeDropdown();
                },

                async deleteProject(project) {
                    if (!confirm(`Are you sure you want to delete the project "${project.name}"?`)) {
                        return;
                    }

                    try {
                        await this.$store.projectStore.deleteProject(project.id);
                    } catch (error) {
                        console.error('Failed to delete project:', error);
                        // TODO: Show user notification
                    }
                },

                createNewProject() {
                    if (this.$store.projectStore) {
                        this.$store.projectStore.openDetailModal('create');
                    }
                    this.closeDropdown();
                },

            };
        }
    </script>

    <style>
        /* Project Indicator Component Styles */
        .project-indicator-container {
            position: relative;
            display: inline-block;
        }

        .project-indicator-button {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            background: var(--color-panel);
            border: 1px solid var(--color-border);
            border-radius: 0.5rem;
            padding: 0.375rem 0.625rem;
            font-size: var(--font-size-small, 0.8rem);
            color: var(--color-text);
            font-weight: 500;
            backdrop-filter: blur(10px);
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
            white-space: nowrap;
            opacity: 0.7;
        }

        .project-indicator-button:hover {
            background: var(--color-secondary);
            border-color: var(--color-primary);
            opacity: 1;
        }

        .project-indicator-button.active {
            background: rgba(64, 196, 255, 0.25);
            border-color: rgba(64, 196, 255, 0.5);
        }

        .project-indicator-button.has-project {
            color: #40c4ff;
            background: rgba(64, 196, 255, 0.1);
            border-color: rgba(64, 196, 255, 0.25);
            opacity: 1;
        }

        .project-indicator-button.has-project:hover {
            background: rgba(64, 196, 255, 0.2);
            border-color: rgba(64, 196, 255, 0.4);
        }

        .project-icon {
            width: 0.9rem;
            height: 0.9rem;
            fill: currentColor;
            flex-shrink: 0;
        }

        .project-name {
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 140px;
            min-width: 0;
        }

        .project-status-dot {
            width: 0.5rem;
            height: 0.5rem;
            background: var(--color-border);
            border-radius: 50%;
            flex-shrink: 0;
            transition: all 0.2s ease;
            opacity: 0.5;
        }

        .project-status-dot.active {
            background: #40c4ff;
            opacity: 1;
            animation: pulse 2s ease-in-out infinite;
        }

        .dropdown-arrow {
            width: 1rem;
            height: 1rem;
            fill: currentColor;
            flex-shrink: 0;
            transition: transform 0.2s ease;
        }

        .dropdown-arrow.rotated {
            transform: rotate(180deg);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Dropdown Styles */
        .project-dropdown {
            position: absolute;
            top: calc(100% + 0.5rem);
            right: 0;
            background: var(--color-background, #131313);
            border: 1px solid var(--color-border, #444444a8);
            border-radius: 0.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            z-index: 2000;
            width: 320px;
            max-height: 70vh;
            overflow-y: auto;
            backdrop-filter: blur(20px);
        }

        .dropdown-section {
            padding: 0.875rem;
        }

        .dropdown-section:first-child {
            padding-top: 1rem;
        }

        .dropdown-section:last-child {
            padding-bottom: 1rem;
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 600;
            font-size: var(--font-size-smaller, 0.9rem);
            color: var(--color-text, #d4d4d4);
            margin-bottom: 0.75rem;
        }

        .project-count {
            font-weight: 400;
            color: var(--color-secondary, #656565);
        }

        .dropdown-divider {
            height: 1px;
            background: var(--color-border, #444444a8);
            margin: 0 1rem;
        }

        /* Active Project Card */
        .active-project-card {
            background: rgba(64, 196, 255, 0.1);
            border: 1px solid rgba(64, 196, 255, 0.2);
            border-radius: 0.5rem;
            padding: 0.75rem;
        }

        .project-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .project-card-name {
            font-weight: 600;
            color: #40c4ff;
        }

        .project-card-actions {
            display: flex;
            gap: 0.25rem;
        }


        .project-card-description {
            font-size: var(--font-size-small, 0.8rem);
            color: var(--color-text, #d4d4d4);
            opacity: 0.8;
        }

        /* Projects List */
        .projects-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .project-list-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--color-border, #444444a8);
            border-radius: 0.5rem;
            padding: 0.75rem;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .project-list-item:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(64, 196, 255, 0.3);
        }

        .project-list-item.is-active {
            background: rgba(64, 196, 255, 0.1);
            border-color: rgba(64, 196, 255, 0.3);
        }

        .project-item-main {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 0.25rem;
        }

        .project-item-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            flex: 1;
            min-width: 0;
        }

        .project-item-name {
            font-weight: 500;
            color: var(--color-text, #d4d4d4);
        }

        .project-item-path {
            font-size: var(--font-size-small, 0.8rem);
            color: var(--color-secondary, #656565);
            font-family: var(--font-family-code, "Roboto Mono", monospace);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .project-item-actions {
            display: flex;
            gap: 0.25rem;
            flex-shrink: 0;
        }

        .project-item-description {
            font-size: var(--font-size-small, 0.8rem);
            color: var(--color-text, #d4d4d4);
            opacity: 0.7;
            margin-top: 0.5rem;
        }

        /* Action Buttons */
        .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 1.5rem;
            height: 1.5rem;
            background-color: transparent;
            border: none;
            border-radius: 3px;
            color: var(--color-text);
            cursor: pointer;
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .action-btn.small {
            width: 1.25rem;
            height: 1.25rem;
        }

        .action-btn:hover {
            opacity: 1;
            background-color: var(--color-secondary);
        }

        .action-btn.danger:hover {
            background-color: var(--color-accent);
            color: white;
        }

        .action-btn svg {
            width: 12px;
            height: 12px;
            fill: currentColor;
            flex-shrink: 0;
        }

        /* No Projects Message */
        .no-projects-message {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 1.5rem;
            color: var(--color-secondary, #656565);
        }

        .no-projects-icon {
            width: 2rem;
            height: 2rem;
            fill: currentColor;
            opacity: 0.5;
            margin-bottom: 0.5rem;
        }

        .no-projects-message small {
            margin-top: 0.25rem;
            font-size: var(--font-size-small, 0.8rem);
            opacity: 0.7;
        }

        /* Loading Message */
        .loading-message {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem;
            color: var(--color-secondary, #656565);
        }

        .loading-spinner {
            width: 1rem;
            height: 1rem;
            border: 2px solid var(--color-border, #444444a8);
            border-top: 2px solid #40c4ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Dropdown Actions */
        .dropdown-actions {
            padding: 0.875rem;
            display: flex;
            flex-direction: column;
            gap: 0.375rem;
            align-items: center;
        }

        .action-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 8px var(--spacing-sm, 0.75rem);
            width: 100%;
            max-width: 240px;
            background-color: transparent;
            border: none;
            border-radius: 5px;
            color: var(--color-text);
            font-family: "Rubik", Arial, Helvetica, sans-serif;
            font-size: 0.7rem;
            font-weight: 400;
            cursor: pointer;
            opacity: 0.8;
            transition: all 0.3s ease;
            text-align: center;
        }

        .action-button:hover {
            opacity: 1;
            background-color: var(--color-secondary);
            border-radius: 4px;
        }

        .action-button:active {
            opacity: 0.5;
        }

        .action-button.primary {
            color: var(--color-text);
            opacity: 0.9;
        }

        .action-button.primary:hover {
            opacity: 1;
            background-color: var(--color-secondary);
        }

        /* Create New Project Section */
        .create-project-section {
            padding: 0.5rem 0.875rem;
            border-bottom: 1px solid var(--color-border, #444444a8);
        }

        .create-project-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            width: 100%;
            padding: 0.625rem 1rem;
            background: transparent;
            border: 1px solid var(--color-border, #444444a8);
            border-radius: 6px;
            color: var(--color-text, #d4d4d4);
            font-family: var(--font-family-main, "Rubik", Arial, Helvetica, sans-serif);
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }

        .create-project-btn:hover {
            background: var(--color-secondary, rgba(255, 255, 255, 0.05));
            border-color: var(--color-primary, rgba(64, 196, 255, 0.4));
        }

        .create-project-icon {
            width: 14px;
            height: 14px;
            fill: currentColor;
            flex-shrink: 0;
        }

        .action-button svg {
            width: 14px;
            height: 14px;
            fill: currentColor;
            flex-shrink: 0;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .project-dropdown {
                right: 0;
                width: 280px;
                max-width: calc(100vw - 2rem);
            }

            .project-name {
                max-width: 100px;
            }

            .project-indicator-button {
                padding: 0.25rem 0.5rem;
                gap: 0.25rem;
            }
        }

        @media (max-width: 480px) {
            .project-dropdown {
                width: 260px;
            }

            .project-name {
                max-width: 80px;
            }
        }
    </style>
</body>

</html>