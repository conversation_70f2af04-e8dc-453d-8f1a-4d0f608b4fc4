/* Project Management System Components */
/* Following Agent Zero design standards with CSS variables and responsive design */

/* Project Indicator Component */
.project-indicator {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    font-family: var(--font-family-main);
    font-size: var(--font-size-normal);
    color: var(--color-text);
}

.project-indicator:hover,
.project-indicator.active {
    background: var(--color-secondary);
    border-color: var(--color-primary);
}

.project-indicator:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.project-indicator-icon {
    width: 16px;
    height: 16px;
    opacity: 0.7;
    transition: opacity var(--transition-speed) ease;
}

.project-indicator:hover .project-indicator-icon {
    opacity: 1;
}

.project-indicator-text {
    font-weight: 500;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.project-indicator-arrow {
    width: 12px;
    height: 12px;
    opacity: 0.6;
    transition: transform var(--transition-speed) ease;
}

.project-indicator.open .project-indicator-arrow {
    transform: rotate(180deg);
}

/* Project Indicator Dropdown */
.project-indicator-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 320px;
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
    margin: 0;

    /* Performance optimizations for <50ms project switching */
    will-change: transform, opacity;
    transform: translateZ(0);
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px) scale(0.95);
}

.project-indicator-dropdown.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}


/* Light mode overrides */
.light-mode .project-indicator,
.light-mode .project-indicator-dropdown {
    background: var(--color-panel-light);
    border-color: var(--color-border-light);
}

.light-mode .project-indicator:hover,
.light-mode .project-indicator.active {
    background: var(--color-secondary-light);
}

/* Project Dropdown Content */
.project-dropdown-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--color-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: var(--color-primary);
    background: var(--color-background);
    border-radius: 12px 12px 0 0;
}

.project-dropdown-actions {
    display: flex;
    gap: 0.5rem;
}

.project-dropdown-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 0.5rem 0;
}

.project-dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    color: var(--color-text);
    font-family: var(--font-family-main);
    font-size: var(--font-size-normal);
}

.project-dropdown-item:hover {
    background: var(--color-secondary);
}

.project-dropdown-item.active {
    background: var(--color-primary);
    color: var(--color-background);
    font-weight: 600;
}

.project-dropdown-item.active .project-item-icon {
    opacity: 1;
}

.project-item-icon {
    width: 16px;
    height: 16px;
    opacity: 0.7;
    flex-shrink: 0;
}

.project-item-content {
    flex: 1;
    min-width: 0;
}

.project-item-name,
.project-item-description {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.project-item-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.project-item-description {
    font-size: var(--font-size-small);
    opacity: 0.8;
}

.project-dropdown-footer {
    padding: 0.75rem 1.5rem;
    border-top: 1px solid var(--color-border);
    background: var(--color-background);
    border-radius: 0 0 12px 12px;
}

/* Project Modal Base Styles */
.project-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2001;
    padding: 1rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

.project-modal-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.project-modal {
    background: var(--color-panel);
    border-radius: 12px;
    border: 1px solid var(--color-border);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    /* Performance optimizations for <100ms target */
    will-change: transform, opacity;
    transform: translateZ(0) scale(0.95) translateY(-20px);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-modal-overlay.visible .project-modal {
    transform: translateZ(0) scale(1) translateY(0);
}

/* Light mode overrides */
.light-mode .project-modal {
    background: var(--color-panel-light);
    border-color: var(--color-border-light);
}

/* Project Modal Layout */
.project-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem 1rem 2rem;
    border-bottom: 1px solid var(--color-border);
    background: var(--color-background);
}

.project-modal-title {
    font-size: var(--font-size-large);
    font-weight: 600;
    color: var(--color-primary);
    margin: 0;
}

.project-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--color-text);
    opacity: 0.7;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-speed) ease;
}

.project-modal-close:hover {
    opacity: 1;
    background: var(--color-secondary);
}

.project-modal-content {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem 2rem;
}

/* Shared scrollbar styles */
.project-indicator-dropdown::-webkit-scrollbar,
.project-modal-content::-webkit-scrollbar {
    width: 6px;
}

.project-indicator-dropdown::-webkit-scrollbar-track,
.project-modal-content::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 6px;
}

.project-indicator-dropdown::-webkit-scrollbar-thumb,
.project-modal-content::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.project-indicator-dropdown::-webkit-scrollbar-thumb:hover,
.project-modal-content::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.7);
}

.project-modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1rem 2rem 1.5rem 2rem;
    border-top: 1px solid var(--color-border);
    background: var(--color-background);
}

/* Light mode overrides */
.light-mode .project-modal-header,
.light-mode .project-modal-actions {
    background: var(--color-background-light);
    border-color: var(--color-border-light);
}

/* Project List Modal Specific Styles */
.project-list-modal .project-modal {
    max-width: 800px;
}

.project-list-search {
    position: relative;
    margin-bottom: 1.5rem;
}

.project-list-search input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    background: var(--color-background);
    color: var(--color-text);
    font-family: var(--font-family-main);
    font-size: var(--font-size-normal);
    transition: all var(--transition-speed) ease;
}

.project-list-search input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(115, 122, 129, 0.1);
}

.project-list-search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    opacity: 0.6;
    pointer-events: none;
}

.project-list-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.project-list-card {
    border: 1px solid var(--color-border);
    border-radius: 12px;
    padding: 1.5rem;
    background: var(--color-background);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    position: relative;
}

.project-list-card:hover,
.project-list-card.active {
    border-color: var(--color-primary);
}

.project-list-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.project-list-card.active {
    background: var(--color-secondary);
}

.project-list-card.active::before {
    content: '';
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    background: var(--color-primary);
    border-radius: 50%;
}

.project-card-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.project-card-icon {
    width: 24px;
    height: 24px;
    opacity: 0.8;
    flex-shrink: 0;
}

.project-card-name {
    font-weight: 600;
    font-size: var(--font-size-large);
    color: var(--color-primary);
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.project-card-description {
    color: var(--color-text);
    opacity: 0.8;
    font-size: var(--font-size-normal);
    line-height: 1.5;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.project-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-small);
    opacity: 0.7;
}

.project-card-date {
    color: var(--color-text);
}

.project-card-status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: var(--font-size-small);
    font-weight: 500;
    text-transform: uppercase;
}

.project-card-status.active {
    background: var(--color-primary);
    color: var(--color-background);
}

.project-card-status.inactive {
    background: var(--color-secondary);
    color: var(--color-text);
}

.project-list-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--color-text);
    opacity: 0.7;
}

.project-list-empty-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 1rem;
    opacity: 0.5;
}

.project-list-empty-text {
    font-size: var(--font-size-large);
    margin-bottom: 0.5rem;
}

.project-list-empty-subtext {
    font-size: var(--font-size-normal);
    opacity: 0.8;
}

/* Light mode overrides */
.light-mode .project-list-search input,
.light-mode .project-list-card {
    background: var(--color-background-light);
    border-color: var(--color-border-light);
}

.light-mode .project-list-card.active {
    background: var(--color-secondary-light);
}

/* Project Detail Modal Specific Styles */
.project-detail-modal .project-modal {
    max-width: 700px;
}

.project-detail-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.project-detail-icon {
    width: 48px;
    height: 48px;
    opacity: 0.8;
    flex-shrink: 0;
}

.project-detail-info h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    color: var(--color-primary);
}

.project-detail-info p {
    margin: 0;
    color: var(--color-text);
    opacity: 0.8;
    line-height: 1.5;
}

.project-detail-sections {
    display: grid;
    gap: 1.5rem;
}

.project-detail-section {
    border: 1px solid var(--color-border);
    border-radius: 8px;
    padding: 1.5rem;
    background: var(--color-background);
}

.project-detail-section h3 {
    margin: 0 0 1rem 0;
    font-size: var(--font-size-large);
    color: var(--color-primary);
    font-weight: 600;
}

.project-detail-field {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: start;
}

.project-detail-field:last-child {
    margin-bottom: 0;
}

.project-detail-label {
    font-weight: 500;
    color: var(--color-text);
    opacity: 0.8;
}

.project-detail-value {
    color: var(--color-text);
    word-break: break-word;
}

.project-detail-value.code {
    font-family: var(--font-family-code);
    background: var(--color-secondary);
    padding: 0.5rem;
    border-radius: 4px;
    font-size: var(--font-size-small);
}

.project-detail-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.project-detail-tag {
    background: var(--color-secondary);
    color: var(--color-text);
    padding: 0.25rem 0.75rem;
    border-radius: 16px;
    font-size: var(--font-size-small);
    font-weight: 500;
}

/* Light mode overrides */
.light-mode .project-detail-section {
    background: var(--color-background-light);
    border-color: var(--color-border-light);
}

.light-mode .project-detail-value.code,
.light-mode .project-detail-tag {
    background: var(--color-secondary-light);
}

/* Form Components */
.project-form {
    display: grid;
    gap: 1.5rem;
}

.project-form-field {
    display: grid;
    gap: 0.5rem;
}

.project-form-field label {
    font-weight: 500;
    color: var(--color-primary);
    font-size: var(--font-size-normal);
}

.project-form-field .field-description {
    font-size: var(--font-size-small);
    color: var(--color-text);
    opacity: 0.8;
    margin-top: 0.25rem;
}

.project-form-field input,
.project-form-field textarea,
.project-form-field select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    background: var(--color-background);
    color: var(--color-text);
    font-family: var(--font-family-main);
    font-size: var(--font-size-normal);
    transition: all var(--transition-speed) ease;
    box-sizing: border-box;
}

.project-form-field input:focus,
.project-form-field textarea:focus,
.project-form-field select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(115, 122, 129, 0.1);
}

.project-form-field textarea {
    min-height: 100px;
    resize: vertical;
    font-family: var(--font-family-code);
}

.project-form-field select {
    cursor: pointer;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px;
    padding-right: 2.5rem;
    appearance: none;
}

/* Field validation states */
.project-form-field.error input,
.project-form-field.error textarea,
.project-form-field.error select {
    border-color: var(--color-accent);
    box-shadow: 0 0 0 3px rgba(207, 102, 121, 0.1);
}

.project-form-field.error .field-error {
    color: var(--color-accent);
    font-size: var(--font-size-small);
    margin-top: 0.25rem;
}

/* Light mode overrides */
.light-mode .project-form-field input,
.light-mode .project-form-field textarea,
.light-mode .project-form-field select {
    background: var(--color-background-light);
    border-color: var(--color-border-light);
}

/* Button Components (following Agent Zero standards) */
.project-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 1.5rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    background: var(--color-background);
    color: var(--color-text);
    font-family: var(--font-family-main);
    font-size: var(--font-size-normal);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    text-decoration: none;
    box-sizing: border-box;
}

.project-button:hover {
    background: var(--color-secondary);
    border-color: var(--color-primary);
    transform: translateY(-1px);
}

.project-button:active {
    transform: translateY(0);
}

.project-button.primary {
    background: #2196f3;
    color: white;
    border-color: #2196f3;
}

.project-button.primary:hover {
    background: #1976d2;
    border-color: #1976d2;
}

.project-button.secondary {
    background: var(--color-secondary);
    color: var(--color-text);
    border-color: var(--color-border);
}

.project-button.danger {
    background: var(--color-accent);
    color: white;
    border-color: var(--color-accent);
}

.project-button.danger:hover {
    background: #b71c1c;
    border-color: #b71c1c;
}

.project-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.project-button:disabled:hover {
    background: var(--color-background);
    border-color: var(--color-border);
    transform: none;
}

.project-button.small {
    padding: 0.25rem 0.75rem;
    font-size: var(--font-size-small);
}

.project-button.large {
    padding: 0.75rem 2rem;
    font-size: var(--font-size-large);
}

.project-button-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

/* Light mode overrides */
.light-mode .project-button {
    background: var(--color-background-light);
    border-color: var(--color-border-light);
}

.light-mode .project-button:hover,
.light-mode .project-button.secondary {
    background: var(--color-secondary-light);
}

/* Status and Notification Components */
.project-status-message {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    font-size: var(--font-size-normal);
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.project-status-message.error {
    background: rgba(207, 102, 121, 0.1);
    border: 1px solid rgba(207, 102, 121, 0.3);
    color: var(--color-accent);
}

.project-status-message.success {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    color: #2e7d32;
}

.project-status-message.warning {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: #f57c00;
}

.project-status-message.info {
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.3);
    color: #1976d2;
}

.project-status-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.project-status-text {
    flex: 1;
    line-height: 1.5;
}

/* Light mode adjustments */
.light-mode .project-status-message.error {
    background: rgba(207, 102, 121, 0.15);
    color: #c62828;
}

.light-mode .project-status-message.success {
    background: rgba(76, 175, 80, 0.15);
    color: #2e7d32;
}

.light-mode .project-status-message.warning {
    background: rgba(255, 193, 7, 0.15);
    color: #f57c00;
}

.light-mode .project-status-message.info {
    background: rgba(33, 150, 243, 0.15);
    color: #1976d2;
}

/* Utility Components */
.project-divider {
    border: none;
    border-top: 1px solid var(--color-border);
    margin: 1.5rem 0;
}

.project-spacer {
    height: 1rem;
}

.project-spacer.large {
    height: 2rem;
}

/* Utility Classes */
.project-text-muted {
    color: var(--color-text);
    opacity: 0.7;
}

.project-flex {
    display: flex;
}

.project-flex-center {
    align-items: center;
    justify-content: center;
}

.project-flex-between {
    justify-content: space-between;
}

.project-flex-1 {
    flex: 1;
}

.project-hidden {
    display: none;
}

.project-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Light mode utilities */
.light-mode .project-divider {
    border-color: var(--color-border-light);
}

/* Loading States and Animations */
.project-loading {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.8;
    font-size: var(--font-size-normal);
    color: var(--color-text);
}

.project-loading::before {
    content: "";
    width: 16px;
    height: 16px;
    border: 2px solid var(--color-border);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: project-spin 1s linear infinite;
}

.project-loading.large::before {
    width: 24px;
    height: 24px;
    border-width: 3px;
}

.project-loading.small::before {
    width: 12px;
    height: 12px;
    border-width: 1.5px;
}

@keyframes project-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Simplified skeleton loading */
.project-skeleton {
    animation: project-skeleton-pulse 1.5s ease-in-out infinite;
    background: var(--color-secondary);
    opacity: 0.6;
}

@keyframes project-skeleton-pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 0.3; }
}

/* Performance optimizations */
.project-modal,
.project-indicator-dropdown {
    will-change: transform, opacity;
    transform: translateZ(0);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .project-modal,
    .project-indicator-dropdown,
    .project-button,
    .project-list-card {
        transition: none;
    }

    .project-loading::before {
        animation: none;
        border-top-color: transparent;
    }

    .project-skeleton {
        animation: none;
        background: var(--color-secondary);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .project-modal {
        margin: 0.5rem;
        max-width: calc(100vw - 1rem);
    }

    .project-modal-header,
    .project-modal-content,
    .project-modal-actions {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .project-list-grid {
        grid-template-columns: 1fr;
    }

    .project-detail-field {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .project-modal-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .project-modal-actions .project-button {
        width: 100%;
    }

    .project-indicator-dropdown {
        left: -1rem;
        right: -1rem;
        min-width: unset;
    }
}

@media (max-width: 480px) {
    .project-modal {
        margin: 0.25rem;
        max-width: calc(100vw - 0.5rem);
        max-height: calc(100vh - 0.5rem);
    }

    .project-detail-header {
        flex-direction: column;
        align-items: flex-start;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .project-modal,
    .project-indicator,
    .project-list-card,
    .project-button {
        border-width: 2px;
    }

    .project-indicator-dropdown {
        box-shadow: 0 0 0 2px var(--color-border);
    }
}

/* Print styles */
@media print {
    .project-modal-overlay,
    .project-indicator-dropdown,
    .project-button {
        display: none !important;
    }

    .project-modal {
        position: static;
        box-shadow: none;
        border: 1px solid #000;
        page-break-inside: avoid;
    }
}