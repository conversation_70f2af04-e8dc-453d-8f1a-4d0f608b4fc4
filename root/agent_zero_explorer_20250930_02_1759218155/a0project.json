{"id": "agent_zero_explorer_20250930_02_1759218155", "name": "agent_zero_explorer_20250930_02", "path": "/tmp/agent-zero-projects/agent_zero_explorer_20250930_02", "description": "Exploration sandbox for Agent Zero: small tools, demos, and experiment scripts.", "instructions": "Purpose: quick experiments, demos and lightweight automations. Deliverables: create README.md with goals and usage, metadata.json, src/ skeleton with an example CLI entrypoint (src/main.py), tests/test_example.py (pytest), .gitignore, and a CI workflow file (.github/workflows/ci.yml) that runs tests. Organize code under /work/agent_zero_explorer_20250930_02. Use safe defaults, document risks, and include a metadata.json with name, description, created_at, and path.", "active": true, "created_at": "2025-09-30T07:42:35.264686", "last_opened_at": "2025-09-30T10:48:59.284909"}