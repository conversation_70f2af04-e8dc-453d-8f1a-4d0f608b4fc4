#!/usr/bin/env python3
"""
Test script to verify that Agent Zero properly recognizes project context changes.
This script simulates the issue where switching from Project A to Project B
should make Agent Zero aware of the new project context.
"""

import asyncio
import sys
import os
import json

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from python.helpers.project_manager import ProjectManager
from python.extensions.message_loop_prompts_after._80_active_project import ActiveProjectContext
from python.extensions.message_loop_prompts_after._79_project_list_awareness import ProjectListAwareness


# Simple LoopData mock
class LoopData:
    def __init__(self):
        self.extras_temporary = {}
        self.extras_persistent = {}


class MockAgent:
    """Mock agent for testing project context switching"""
    
    def __init__(self, name="TestAgent"):
        self.agent_name = name
        self.data = {}
        
    def get_data(self, key):
        return self.data.get(key)
        
    def set_data(self, key, value):
        self.data[key] = value
        
    def read_prompt(self, template_file, **kwargs):
        """Mock prompt reading - just return a formatted string"""
        if template_file == "agent.context.active_project.md":
            project_name = kwargs.get('project_name', 'Unknown')
            project_description = kwargs.get('project_description', 'No description')
            project_directory = kwargs.get('project_directory', 'Unknown')
            instructions = kwargs.get('project_instructions')
            
            result = f"## ACTIVE PROJECT: {project_name}\n\n"
            result += f"**Description**: {project_description}\n\n"
            if instructions:
                result += f"**Instructions**: {instructions}\n\n"
            result += f"**Working Directory**: {project_directory}\n\n"
            result += "**Files**: (mock file structure)\n"
            return result
            
        elif template_file == "agent.context.project_list.md":
            has_projects = kwargs.get('has_projects', False)
            projects = kwargs.get('projects', [])
            
            if has_projects:
                result = "## AVAILABLE PROJECTS\n\n"
                for project in projects:
                    result += f"• **{project['name']}**: {project['description']}\n"
                result += "\nUse `project_manager` tool to list, activate, create, or edit projects.\n"
            else:
                result = "## PROJECT SYSTEM\n\nUse `project_manager` tool to create organized workspaces.\n"
            return result
            
        return f"Mock template: {template_file}"


async def test_project_context_switching():
    """Test that project context switching works properly"""
    
    PrintStyle(font_color="blue", padding=True).print("🧪 Testing Project Context Switching")
    
    # Create mock agent
    agent = MockAgent("A0")
    
    # Create project manager
    project_manager = ProjectManager()
    
    # Get all projects
    projects = project_manager.get_all_projects()
    
    if len(projects) < 2:
        PrintStyle(font_color="red", padding=True).print(
            "❌ Need at least 2 projects to test switching. Please create some projects first."
        )
        return False
    
    PrintStyle(font_color="green", padding=True).print(f"✅ Found {len(projects)} projects")
    
    # Test 1: Initial state (no active project)
    PrintStyle(font_color="cyan", padding=True).print("\n📋 Test 1: Initial state (no active project)")
    
    loop_data = LoopData()
    
    # Run project context extensions
    project_list_ext = ProjectListAwareness()
    project_list_ext.agent = agent
    await project_list_ext.execute(loop_data)
    
    active_project_ext = ActiveProjectContext()
    active_project_ext.agent = agent
    await active_project_ext.execute(loop_data)
    
    print("Extras after initial state:")
    for key, value in loop_data.extras_temporary.items():
        print(f"  {key}: {value[:100]}..." if len(str(value)) > 100 else f"  {key}: {value}")
    
    # Test 2: Activate first project
    PrintStyle(font_color="cyan", padding=True).print(f"\n📋 Test 2: Activate Project A ({projects[0].name})")
    
    # Simulate project activation
    agent.set_data("active_project", projects[0].id)
    agent.set_data("active_project_entity", projects[0].to_dict())
    agent.set_data("project_context_refresh", True)
    
    loop_data = LoopData()
    
    # Run project context extensions
    project_list_ext = ProjectListAwareness()
    project_list_ext.agent = agent
    await project_list_ext.execute(loop_data)
    
    active_project_ext = ActiveProjectContext()
    active_project_ext.agent = agent
    await active_project_ext.execute(loop_data)
    
    print("Extras after activating Project A:")
    for key, value in loop_data.extras_temporary.items():
        print(f"  {key}: {value[:100]}..." if len(str(value)) > 100 else f"  {key}: {value}")
    
    # Test 3: Switch to second project
    PrintStyle(font_color="cyan", padding=True).print(f"\n📋 Test 3: Switch to Project B ({projects[1].name})")
    
    # Simulate project switch
    agent.set_data("active_project", projects[1].id)
    agent.set_data("active_project_entity", projects[1].to_dict())
    agent.set_data("project_context_refresh", True)  # This flag indicates context changed
    
    loop_data = LoopData()
    
    # Run project context extensions
    project_list_ext = ProjectListAwareness()
    project_list_ext.agent = agent
    await project_list_ext.execute(loop_data)
    
    active_project_ext = ActiveProjectContext()
    active_project_ext.agent = agent
    await active_project_ext.execute(loop_data)
    
    print("Extras after switching to Project B:")
    for key, value in loop_data.extras_temporary.items():
        print(f"  {key}: {value[:100]}..." if len(str(value)) > 100 else f"  {key}: {value}")
    
    # Test 4: Deactivate all projects
    PrintStyle(font_color="cyan", padding=True).print("\n📋 Test 4: Deactivate all projects")
    
    # Simulate project deactivation
    agent.set_data("active_project", None)
    agent.set_data("active_project_entity", None)
    agent.set_data("project_context_refresh", True)
    
    loop_data = LoopData()
    
    # Run project context extensions
    project_list_ext = ProjectListAwareness()
    project_list_ext.agent = agent
    await project_list_ext.execute(loop_data)
    
    active_project_ext = ActiveProjectContext()
    active_project_ext.agent = agent
    await active_project_ext.execute(loop_data)
    
    print("Extras after deactivating all projects:")
    for key, value in loop_data.extras_temporary.items():
        print(f"  {key}: {value[:100]}..." if len(str(value)) > 100 else f"  {key}: {value}")
    
    PrintStyle(font_color="green", padding=True).print("\n✅ Project context switching test completed!")
    
    return True


if __name__ == "__main__":
    asyncio.run(test_project_context_switching())
